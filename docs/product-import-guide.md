# Product Import Guide

## Overview
The enhanced ProductImporter allows you to import products with custom data processing before the import happens. This guide explains how to use the various features and customizations available.

## Features

### 1. Data Transformation
The importer automatically transforms data before saving:

- **Product Name**: Converts to title case and trims whitespace
- **Price**: Removes currency symbols and converts to numeric value
- **SKU**: Converts to uppercase and trims whitespace
- **Barcode**: Removes special characters and spaces
- **Description**: Strips HTML tags and trims whitespace

### 2. Automatic Data Generation
- **SKU Generation**: If no SKU is provided, one is automatically generated from the product name
- **Slug Generation**: Product slug is automatically created from the name
- **Default Values**: Sets default values for visibility, shipping requirements, etc.

### 3. Category and Brand Processing
- **Categories**: Supports multiple categories separated by commas
- **Brands**: Automatically creates brands if they don't exist
- **Auto-creation**: Categories and brands are created automatically if they don't exist

### 4. Custom Validation
- **Duplicate Prevention**: Checks for existing products by SKU or barcode
- **Price Validation**: Ensures price is in correct numeric format
- **Custom Rules**: Additional validation rules for data integrity

### 5. Advanced Features
- **Weight Processing**: Handles weight data with units
- **Dimensions**: Processes dimension data (length x width x height)
- **Inventory Setup**: Creates default inventory records
- **Price Records**: Creates proper price records with currency support

## CSV Format

### Required Columns
- `name`: Product name (required)
- `amount`: Price in VND (required)

### Optional Columns
- `sku`: Product SKU (auto-generated if empty)
- `barcode`: Product barcode
- `description`: Product description
- `categories`: Categories separated by commas
- `brand`: Brand name
- `weight`: Weight in kg
- `dimensions`: Dimensions in format "length x width x height" (cm)

### Example CSV
```csv
name,amount,sku,barcode,description,categories,brand,weight,dimensions
"iPhone 14 Pro",25000000,"IP14PRO","1234567890123","Latest iPhone","Electronics,Smartphones","Apple",0.206,"7.85x16.02x0.79"
"Samsung Galaxy S23",20000000,"","2345678901234","Premium Android phone","Electronics,Smartphones","Samsung",0.168,"7.06x14.61x0.76"
```

## Usage Instructions

### 1. Prepare Your CSV File
- Use the template provided in `storage/app/public/product_import_template.csv`
- Ensure all required columns are present
- Use proper formatting for prices (numbers only, no currency symbols)
- Separate multiple categories with commas

### 2. Import Process
1. Go to the Products section in your admin panel
2. Click on "Import" button
3. Upload your CSV file
4. Review the mapping of columns
5. Start the import process

### 3. Data Processing Flow
1. **Before Validation**: Custom validation rules are applied
2. **Data Transformation**: All data is cleaned and transformed
3. **Custom Processing**: SKUs, slugs, and relationships are processed
4. **Record Creation**: Products are created or updated
5. **After Save**: Prices, categories, and inventory are set up

## Customization Options

### Adding New Columns
To add new columns to the import:

```php
ImportColumn::make('your_field')
    ->label('Your Field Label')
    ->rules(['nullable', 'max:255'])
    ->transform(function ($value) {
        // Custom transformation logic
        return $value ? trim($value) : null;
    }),
```

### Custom Data Processing
Add custom logic in the `processCustomData()` method:

```php
protected function processCustomData(): void
{
    // Your custom processing logic here
    if (!empty($this->data['custom_field'])) {
        $this->data['processed_field'] = $this->processCustomField($this->data['custom_field']);
    }
    
    // Call parent method to maintain existing functionality
    parent::processCustomData();
}
```

### Custom Validation
Add custom validation in the `validateCustomRules()` method:

```php
protected function validateCustomRules(): void
{
    // Your custom validation logic
    if (!empty($this->data['custom_field']) && !$this->isValidCustomField($this->data['custom_field'])) {
        $this->addError('custom_field', 'Invalid custom field format');
    }
}
```

## Error Handling

### Common Issues
1. **Invalid Price Format**: Ensure prices are numeric values without currency symbols
2. **Duplicate SKUs**: The system will update existing products with the same SKU
3. **Missing Required Fields**: Name and amount are required for all products
4. **Invalid Dimensions**: Use format "length x width x height" for dimensions

### Error Messages
- The importer provides detailed error messages for each row that fails
- Check the import results for specific error details
- Failed rows can be exported and corrected for re-import

## Best Practices

1. **Test with Small Batches**: Start with a small number of products to test the import
2. **Backup Data**: Always backup your database before large imports
3. **Validate Data**: Check your CSV file for formatting issues before import
4. **Use Templates**: Use the provided template as a starting point
5. **Monitor Progress**: Watch the import progress and check for errors

## Support

For additional customization or issues with the import process, refer to the ProductImporter class in `app/Filament/Imports/ProductImporter.php`.

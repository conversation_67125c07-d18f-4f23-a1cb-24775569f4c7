<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\HasMany;
use Shopper\Core\Models\OrderItem as Model;

class OrderItem extends Model
{
    /**
     * Get all taxes applied to this order item
     */
    public function taxes(): HasMany
    {
        return $this->hasMany(OrderItemTax::class);
    }

    /**
     * Get total tax amount for this order item
     */
    public function getTotalTaxAmount(): int
    {
        return $this->taxes()->sum('tax_amount');
    }

    /**
     * Get formatted total tax amount
     */
    public function getFormattedTotalTaxAmount(): string
    {
        return shopper_money_format($this->getTotalTaxAmount(), $this->order->currency_code);
    }

    /**
     * Get order item total including taxes
     */
    public function getTotalWithTaxes(): int
    {
        return ($this->unit_price_amount * $this->quantity) + $this->getTotalTaxAmount();
    }

    /**
     * Get formatted order item total including taxes
     */
    public function getFormattedTotalWithTaxes(): string
    {
        return shopper_money_format($this->getTotalWithTaxes(), $this->order->currency_code);
    }

    /**
     * Get tax amount per unit
     */
    public function getTaxAmountPerUnit(): int
    {
        return $this->quantity > 0 ? (int) round($this->getTotalTaxAmount() / $this->quantity) : 0;
    }

    /**
     * Get formatted tax amount per unit
     */
    public function getFormattedTaxAmountPerUnit(): string
    {
        return shopper_money_format($this->getTaxAmountPerUnit(), $this->order->currency_code);
    }

    /**
     * Get unit price including taxes
     */
    public function getUnitPriceWithTaxes(): int
    {
        return $this->unit_price_amount + $this->getTaxAmountPerUnit();
    }

    /**
     * Get formatted unit price including taxes
     */
    public function getFormattedUnitPriceWithTaxes(): string
    {
        return shopper_money_format($this->getUnitPriceWithTaxes(), $this->order->currency_code);
    }

    /**
     * Get tax breakdown for this order item
     */
    public function getTaxBreakdown(): array
    {
        return $this->taxes()
            ->get()
            ->map(function ($tax) {
                return [
                    'tax_code' => $tax->tax_code,
                    'tax_name' => $tax->tax_name,
                    'tax_rate' => $tax->tax_rate,
                    'tax_amount' => $tax->tax_amount,
                    'taxable_amount' => $tax->taxable_amount,
                    'is_inclusive' => $tax->is_inclusive,
                    'is_compound' => $tax->is_compound,
                    'formatted_amount' => $tax->formatted_tax_amount,
                ];
            })
            ->toArray();
    }

    /**
     * Check if order item has any taxes
     */
    public function hasTaxes(): bool
    {
        return $this->taxes()->exists();
    }
}

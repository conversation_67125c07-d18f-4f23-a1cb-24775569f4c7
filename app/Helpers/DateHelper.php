<?php

if (!function_exists('vietnamese_day_name')) {
    /**
     * Get Vietnamese day name from date
     *
     * @param string|Carbon $date
     * @return string
     */
    function vietnamese_day_name($date)
    {
        $dayOfWeek = \Carbon\Carbon::parse($date)->dayOfWeek;
        $vietnameseDays = [
            0 => 'Chủ nhật',
            1 => 'Thứ 2', 
            2 => 'Thứ 3',
            3 => 'Thứ 4',
            4 => 'Thứ 5',
            5 => 'Thứ 6',
            6 => 'Thứ 7'
        ];
        
        return $vietnameseDays[$dayOfWeek];
    }
}

if (!function_exists('vietnamese_formatted_date')) {
    /**
     * Get formatted Vietnamese date with day name
     *
     * @param string|Carbon $date
     * @param string $format
     * @return string
     */
    function vietnamese_formatted_date($date, $format = 'd/m/Y')
    {
        $carbonDate = \Carbon\Carbon::parse($date);
        $vietnameseDay = vietnamese_day_name($date);
        
        return $vietnameseDay . ', ' . $carbonDate->format($format);
    }
}

if (!function_exists('vietnamese_short_date')) {
    /**
     * Get short Vietnamese date format
     *
     * @param string|Carbon $date
     * @return string
     */
    function vietnamese_short_date($date)
    {
        return vietnamese_formatted_date($date, 'd/m');
    }
}

if (!function_exists('vietnamese_long_date')) {
    /**
     * Get long Vietnamese date format
     *
     * @param string|Carbon $date
     * @return string
     */
    function vietnamese_long_date($date)
    {
        return vietnamese_formatted_date($date, 'd/m/Y H:i');
    }
}

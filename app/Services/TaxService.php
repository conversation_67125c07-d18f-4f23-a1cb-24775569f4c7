<?php

declare(strict_types=1);

namespace App\Services;

use Shopper\Core\Models\Order;
use Shopper\Core\Models\OrderItem;
use Illuminate\Support\Collection;
use Shopper\Core\Models\OrderItemTax;
use Shopper\Core\Models\OrderTax;
use Shopper\Core\Models\Tax;

class TaxService
{
    /**
     * Calculate taxes for an order
     */
    public function calculateOrderTaxes(Order $order): array
    {
        $orderTaxes = [];
        $totalTaxAmount = 0;

        // Get all order items with their products
        $orderItems = $order->items()->with(['product.taxes.taxRates'])->get();

        foreach ($orderItems as $orderItem) {
            $itemTaxes = $this->calculateOrderItemTaxes($orderItem, $order->zone_id, $order->currency_id);

            foreach ($itemTaxes['tax_breakdown'] as $taxData) {
                $taxId = $taxData['tax_id'];

                if (!isset($orderTaxes[$taxId])) {
                    $orderTaxes[$taxId] = [
                        'tax_id' => $taxId,
                        'tax_name' => $taxData['tax_name'],
                        'tax_code' => $taxData['tax_code'],
                        'tax_rate' => $taxData['tax_rate'],
                        'tax_amount' => 0,
                        'taxable_amount' => 0,
                        'is_inclusive' => $taxData['is_inclusive'],
                        'is_compound' => $taxData['is_compound'],
                    ];
                }

                $orderTaxes[$taxId]['tax_amount'] += $taxData['tax_amount'];
                $orderTaxes[$taxId]['taxable_amount'] += $taxData['taxable_amount'];
            }

            $totalTaxAmount += $itemTaxes['total_tax_amount'];
        }

        return [
            'total_tax_amount' => $totalTaxAmount,
            'tax_breakdown' => array_values($orderTaxes),
        ];
    }

    /**
     * Calculate taxes for an order item
     */
    public function calculateOrderItemTaxes(OrderItem $orderItem, ?int $zoneId = null, ?int $currencyId = null): array
    {
        $product = $orderItem->product;

        if (!$product || !method_exists($product, 'hasTaxes') || !$product->hasTaxes()) {
            return [
                'total_tax_amount' => 0,
                'tax_breakdown' => [],
            ];
        }

        $baseAmount = $orderItem->unit_price_amount * $orderItem->quantity;
        $taxCalculation = $product->calculateTaxAmount($baseAmount, $zoneId, $currencyId);

        return $taxCalculation;
    }

    /**
     * Save calculated taxes to order
     */
    public function saveOrderTaxes(Order $order, array $taxCalculation): void
    {
        // Clear existing order taxes
        $order->taxes()->delete();

        foreach ($taxCalculation['tax_breakdown'] as $taxData) {
            OrderTax::create([
                'order_id' => $order->id,
                'tax_id' => $taxData['tax_id'],
                'tax_name' => $taxData['tax_name'],
                'tax_code' => $taxData['tax_code'],
                'tax_rate' => $taxData['tax_rate'],
                'tax_amount' => $taxData['tax_amount'],
                'taxable_amount' => $taxData['taxable_amount'],
                'is_inclusive' => $taxData['is_inclusive'],
                'is_compound' => $taxData['is_compound'],
            ]);
        }
    }

    /**
     * Save calculated taxes to order items
     */
    public function saveOrderItemTaxes(OrderItem $orderItem, array $taxCalculation): void
    {
        // Clear existing order item taxes
        $orderItem->taxes()->delete();

        foreach ($taxCalculation['tax_breakdown'] as $taxData) {
            OrderItemTax::create([
                'order_item_id' => $orderItem->id,
                'tax_id' => $taxData['tax_id'],
                'tax_name' => $taxData['tax_name'],
                'tax_code' => $taxData['tax_code'],
                'tax_rate' => $taxData['tax_rate'],
                'tax_amount' => $taxData['tax_amount'],
                'taxable_amount' => $taxData['taxable_amount'],
                'quantity' => $orderItem->quantity,
                'is_inclusive' => $taxData['is_inclusive'],
                'is_compound' => $taxData['is_compound'],
            ]);
        }
    }

    /**
     * Get tax summary for reporting
     */
    public function getTaxSummary(Collection $orders): array
    {
        $taxSummary = [];

        foreach ($orders as $order) {
            $orderTaxes = $order->taxes;

            foreach ($orderTaxes as $orderTax) {
                $taxCode = $orderTax->tax_code;

                if (!isset($taxSummary[$taxCode])) {
                    $taxSummary[$taxCode] = [
                        'tax_name' => $orderTax->tax_name,
                        'tax_code' => $taxCode,
                        'total_tax_amount' => 0,
                        'total_taxable_amount' => 0,
                        'order_count' => 0,
                    ];
                }

                $taxSummary[$taxCode]['total_tax_amount'] += $orderTax->tax_amount;
                $taxSummary[$taxCode]['total_taxable_amount'] += $orderTax->taxable_amount;
                $taxSummary[$taxCode]['order_count']++;
            }
        }

        return array_values($taxSummary);
    }

    /**
     * Get applicable taxes for a product in a specific zone
     */
    public function getApplicableTaxes(int $productId, ?int $zoneId = null, ?int $currencyId = null): Collection
    {
        $product = app(config('shopper.models.product'))->find($productId);

        if (!$product || !method_exists($product, 'activeTaxes')) {
            return collect();
        }

        return $product->activeTaxes()
            ->with(['taxRates' => function ($query) use ($zoneId, $currencyId) {
                $query->effective()
                    ->forZone($zoneId)
                    ->forCurrency($currencyId);
            }])
            ->get()
            ->filter(function ($tax) {
                return $tax->taxRates->isNotEmpty();
            });
    }

    /**
     * Validate tax configuration
     */
    public function validateTaxConfiguration(Tax $tax): array
    {
        $errors = [];

        if (empty($tax->name)) {
            $errors[] = 'Tax name is required';
        }

        if (empty($tax->code)) {
            $errors[] = 'Tax code is required';
        }

        if (!in_array($tax->type, ['percentage', 'fixed_amount'])) {
            $errors[] = 'Invalid tax type';
        }

        if ($tax->is_compound && $tax->priority <= 0) {
            $errors[] = 'Compound taxes must have a priority greater than 0';
        }

        // Check for duplicate tax codes
        $duplicateExists = Tax::where('code', $tax->code)
            ->where('id', '!=', $tax->id)
            ->exists();

        if ($duplicateExists) {
            $errors[] = 'Tax code must be unique';
        }

        return $errors;
    }

    /**
     * Get tax rates for a specific zone and currency
     */
    public function getTaxRatesForZone(?int $zoneId = null, ?int $currencyId = null): Collection
    {
        return Tax::active()
            ->with(['taxRates' => function ($query) use ($zoneId, $currencyId) {
                $query->effective()
                    ->forZone($zoneId)
                    ->forCurrency($currencyId);
            }])
            ->get()
            ->filter(function ($tax) {
                return $tax->taxRates->isNotEmpty();
            });
    }
}

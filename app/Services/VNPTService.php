<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\EInvoiceConfig;
use Shopper\Core\Models\Order;

class VNPTService
{
    public $configuration;
    public $order;

    public function __construct(Order $order)
    {
        $this->configuration = EInvoiceConfig::where('provider_code', 'VNPT')->where('is_enabled', true)->first();
        $this->order = $order;
    }

    public function createInvoiceVat()
    {

        if (!$this->configuration) {
            return false;
        }
        $customer = $this->order->customer;
        $taxtable = [
            'kct' => [
                'subtotal' => 0,
                'name' => 'KCT',
                'rate' => '-1',
                'total' => 0,
                'tax' => 0
            ]
        ];

        $items = [];
        $subtotal = $this->order->price_amount;
        $total = $this->order->total_amount;
        $dvt = 'Cái';

        foreach ($this->order->items as $key => $item) {

            $TSuat = 0;
            if (!empty($item->product->getApplicableTaxRates())) {
                foreach ($item->product->getApplicableTaxRates() as $key => $taxRate) {
                    $TSuat += $taxRate['effective_rate'];
                    $indexRate = format_tax_rate((float) $taxRate['effective_rate']);
                    if (!isset($taxtable[$indexRate])) {
                        $taxtable[$indexRate] = [
                            'subtotal' => 0,
                            'name' => $indexRate,
                            'rate' => (int)$taxRate['effective_rate'],
                            'total' => 0,
                            'tax' => 0
                        ];
                    }
                    $taxtable[$indexRate]['subtotal'] += $item->unit_price_amount;
                    $taxtable[$indexRate]['total'] += $item->unit_price_amount * $item->quantity;
                    $taxtable[$indexRate]['tax'] += round(($item->unit_price_amount * $item->quantity) * ($taxtable[$indexRate]['rate'] / 100));
                }
            } else {
                $taxtable['kct']['subtotal'] += $item->unit_price_amount;
                $taxtable['kct']['total'] += $item->unit_price_amount * $item->quantity;
            }
            $ThTien = $item->unit_price_amount * $item->quantity;
            $TThue = $item->getTotalTaxAmount();
            $TSThue = round($ThTien  + $TThue);

            $items[] = [
                'id' => $item->product_id,
                'MHHDVu' => 'DV',
                'THHDVu' => $item->name,
                'DVTinh' => $dvt,
                'SLuong' => $item->quantity,
                'DGia' => $item->unit_price_amount,
                'TLCKhau' => 0,
                'STCKhau' => 0,
                'TSuat' => $TSuat > 0 ? $TSuat : '-1',
                'ThTien' =>  $ThTien,
                'TThue' => $TThue,
                'TSThue' => $TSThue,
            ];
        }

        $LTSuat = [];
        $TgTCThue = 0;
        $TgTThue = 0;

        foreach ($taxtable as  $tax) {

            $LTSuat[] = [
                'TSuat' => $tax['rate'],
                'TThue' => $tax['tax'],
                'ThTien' => round($tax['total']),
            ];

            $TgTCThue += round($tax['total']);
            if ($tax['name'] != 'KCT') {
                $TgTThue += $tax['tax'];
            }
        }


        $data = [
            'fkey' => $this->order->number,
            // 'client' => $client,
            'items' => $items,
            'THTTLTSuat' => $LTSuat,
            'TgTCThue' => round($TgTCThue),
            'TgTThue' => round($TgTThue),
            'TgTTTBSo' => round($total),
            'TgTTTBChu' => round($total),
        ];
        dd($data);
        $xml = $this->createInvoiceXml($data);
        $this->sendVNPTInvoice($this->configuration->link_api . '/PublishService.asmx', $xml);
    }

    private function createInvoiceXml($data)
    {
    }

    private function sendVNPTInvoice($url, $xml, $method = 'POST')
    {
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_POSTFIELDS => $xml,
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/soap+xml; charset=utf-8'
            ),
        ));
        $response = curl_exec($curl);
        curl_close($curl);
        return $response;
    }
}

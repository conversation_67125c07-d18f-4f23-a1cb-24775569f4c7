<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\EInvoiceConfig;
use DOMDocument;
use Shopper\Core\Models\Order;

class VNPTService
{
    public $configuration;
    public $order;

    public function __construct(Order $order)
    {
        $this->configuration = EInvoiceConfig::where('provider_code', 'VNPT')->where('is_enabled', true)->first();
        $this->order = $order;
    }

    public function createInvoiceVat()
    {

        if (!$this->configuration) {
            return [
                'status' => false,
                'message' => 'Cấu hình hoá đơn điện tử chưa được thiết lập!',
            ];
        }
        $customer = $this->order->customer;
        $taxtable = [
            'kct' => [
                'subtotal' => 0,
                'name' => 'KCT',
                'rate' => '-1',
                'total' => 0,
                'tax' => 0
            ]
        ];

        $items = [];
        $subtotal = $this->order->price_amount;
        $total = $this->order->total_amount;
        $dvt = 'Cái';

        foreach ($this->order->items as $key => $item) {

            $TSuat = 0;
            if (!empty($item->product->getApplicableTaxRates())) {
                foreach ($item->product->getApplicableTaxRates() as $key => $taxRate) {
                    $TSuat += $taxRate['effective_rate'];
                    $indexRate = format_tax_rate((float) $taxRate['effective_rate']);
                    if (!isset($taxtable[$indexRate])) {
                        $taxtable[$indexRate] = [
                            'subtotal' => 0,
                            'name' => $indexRate,
                            'rate' => (int)$taxRate['effective_rate'],
                            'total' => 0,
                            'tax' => 0
                        ];
                    }
                    $taxtable[$indexRate]['subtotal'] += $item->unit_price_amount;
                    $taxtable[$indexRate]['total'] += $item->unit_price_amount * $item->quantity;
                    $taxtable[$indexRate]['tax'] += round(($item->unit_price_amount * $item->quantity) * ($taxtable[$indexRate]['rate'] / 100));
                }
            } else {
                $taxtable['kct']['subtotal'] += $item->unit_price_amount;
                $taxtable['kct']['total'] += $item->unit_price_amount * $item->quantity;
            }
            $ThTien = $item->unit_price_amount * $item->quantity;
            $TThue = $item->getTotalTaxAmount();
            $TSThue = round($ThTien  + $TThue);

            $items[] = [
                'id' => $item->product_id,
                'MHHDVu' => 'DV',
                'THHDVu' => $item->name,
                'DVTinh' => $dvt,
                'SLuong' => $item->quantity,
                'DGia' => $item->unit_price_amount,
                'TLCKhau' => 0,
                'STCKhau' => 0,
                'TSuat' => $TSuat > 0 ? $TSuat : '-1',
                'ThTien' =>  $ThTien,
                'TThue' => $TThue,
                'TSThue' => $TSThue,
            ];
        }

        $LTSuat = [];
        $TgTCThue = $this->order->price_amount;
        $TgTThue = 0;

        foreach ($taxtable as  $tax) {

            $LTSuat[] = [
                'TSuat' => $tax['rate'],
                'TThue' => $tax['tax'],
                'ThTien' => round($tax['total']),
            ];

            if ($tax['name'] != 'KCT') {
                $TgTThue += $tax['tax'];
            }
        }

        $client = [
            'clientname' => '',
            'companyname' => '',
            'taxid' => '',
            'phonenumber' => '',
            'email' =>  '',
            'address' =>  '',
            'nationalid' =>  '',
        ];


        $data = [
            'fkey' => $this->order->number,
            'client' => $client,
            'items' => $items,
            'THTTLTSuat' => $LTSuat,
            'TgTCThue' => round($TgTCThue),
            'TgTThue' => round($TgTThue),
            'TgTTTBSo' => round($total),
            'TgTTTBChu' => round($total),
        ];
        $xml = $this->createInvoiceXml($data);

        $result = $this->sendVNPTInvoice($this->configuration->config['link_api'] . '/PublishService.asmx', $xml->saveXML());

        $importAndPublishInvMTTResult = (string)$result->Body->ImportAndPublishInvMTTResponse->ImportAndPublishInvMTTResult;
        if (substr($importAndPublishInvMTTResult, 0, 3) == 'OK:') {
            //OK:1/004;C25MTN-TNP_00136_9_M1-25-43838-00900000009
            $rep = ltrim($importAndPublishInvMTTResult, 'OK:');
            $resp = explode('_', $rep);
            $array1 = explode("-", $resp[0]);
            $fkey = $array1[1];
            $array2 = explode(";", $array1[0]);
            $pattern = $array2[0];
            $serial = $array2[1];

            $invoice_number = sprintf('%08d', $resp[1]);

            $authority_issued_code = $resp[2];

            $data = [
                'provider_code' => 'VNPT',
                'invoice_check_url' => $this->configuration->config['link_api'],
                'fkey' => $fkey,
                'pattern' => $pattern,
                'serial' => $serial,
                'invoice_number' => $invoice_number,
                'authority_issued_code' => $authority_issued_code,
            ];

            return [
                'status' => true,
                'data' => $data,
            ];
        } else {
            $error_list = [
                'ERR:1' => 'Tài khoản đăng nhập sai hoặc không có quyền thêm mới hóa đơn	',
                'ERR:2' => 'Pattern hoặc serial truyền vào rỗng	',
                'ERR:3' => 'Dữ liệu xml đầu vào không đúng quy định	',
                'ERR:4' => 'Không lấy được thông tin công ty (currentCompany null)	',
                'ERR:6' => 'Không đủ số lượng hóa đơn cho lô thêm mới	',
                'ERR:7' => 'User name không phù hợp, không tìm thấy user.	',
                'ERR:11'    => 'Pattern hoặc serial không đúng định dạng	',
                'ERR:13'    => 'Danh sách hóa đơn tồn tại hóa đơn trùng Fkey	',
                'ERR:15'    => 'Ngày lập truyền vào lớn hơn ngày hiện tại hoặc XML không đúng định dạng (hóa đơn ngoại tệ không truyền tỷ giá) ',
                'ERR:20'    => 'Pattern và serial không phù hợp, hoặc không tồn tại hóa đơn đã đăng ký có sử dụng Pattern và Serial truyền vào',
                'ERR:5' => 'Không phát hành được hóa đơn	Lỗi không xác định. DB roll back',
                'ERR:10'    => 'Lô có số hóa đơn vượt quá max cho phép	Mặc định là 5000, hoặc được cấu hình theo từng app',
                'ERR:21'    => 'Trùng số hóa đơn',
                'ERR:22'    => 'Thông tin người bán vượt maxlength',
                'ERR:23'    => 'Mã CQT rỗng	',
                'ERR:30'    => 'Danh sách hóa đơn tồn tại ngày hóa đơn nhỏ hơn ngày hóa đơn đã phát hành',
            ];

            $message = $error_list[trim($importAndPublishInvMTTResult)] ?? 'Lỗi không xác định.';
            return [
                'status' => false,
                'message' => $message,
            ];
        }
    }

    private function createInvoiceXml($data)
    {


        $xml = new DOMDocument();

        $envelope = $xml->createElementNS('http://www.w3.org/2003/05/soap-envelope', 'soap12:Envelope');
        $envelope->setAttribute('xmlns:xsi', 'http://www.w3.org/2001/XMLSchema-instance');
        $envelope->setAttribute('xmlns:xsd', 'http://www.w3.org/2001/XMLSchema');
        $envelope->setAttribute('xmlns:soap12', 'http://www.w3.org/2003/05/soap-envelope');

        $body = $xml->createElementNS('http://www.w3.org/2003/05/soap-envelope', 'soap12:Body');

        $import = $xml->createElement('ImportAndPublishInvMTT');
        // $import = $xml->createElement('ImportInvByPattern');
        //  $import = $xml->createElement('ImportInvByPatternMTT');
        $import->setAttribute('xmlns', 'http://tempuri.org/');

        $account = $xml->createElement('Account', $this->configuration->config['account']);
        $import->appendChild($account);

        $acpass = $xml->createElement('ACpass', $this->configuration->config['account_pass']);
        $import->appendChild($acpass);



        $xmlInvData = $xml->createElement('xmlInvData');
        $xmlInvData->appendChild($xml->createCDATASection($this->buildDSHDon($data)));
        $import->appendChild($xmlInvData);



        $username = $xml->createElement('username', $this->configuration->config['username']);
        $import->appendChild($username);

        $password = $xml->createElement('password', $this->configuration->config['username_pass']);
        $import->appendChild($password);

        $pattern = $xml->createElement('pattern', $this->configuration->config['pattern_cash_register']);
        $import->appendChild($pattern);

        $serial = $xml->createElement('serial', $this->configuration->config['serial_cash_register']);
        $import->appendChild($serial);

        $convert = $xml->createElement('convert', '0');
        $import->appendChild($convert);


        $body->appendChild($import);
        $envelope->appendChild($body);
        $xml->appendChild($envelope);

        return $xml;
    }

    private function buildDSHDon($data)
    {
        $items = $data['items'];
        $taxtable = $data['THTTLTSuat'];
        $client = $data['client'];

        $xml = new DOMDocument();
        $dshdon = $xml->createElement('DSHDon');
        $hdon = $xml->createElement('HDon');
        $key = $xml->createElement('key', $data['fkey']);
        $hdon->appendChild($key);

        $dlhdon = $xml->createElement('DLHDon');
        $ttchung = $xml->createElement('TTChung');
        // $ttchung->appendChild($xml->createElement('NLap'));
        $dvtte = $xml->createElement('DVTTe', 'VND');
        $ttchung->appendChild($dvtte);
        // $ttchung->appendChild($xml->createElement('TGia'));
        $httt = $xml->createElement('HTTToan', 'TM/CK');
        $ttchung->appendChild($httt);
        $dlhdon->appendChild($ttchung);


        $ndhdon = $xml->createElement('NDHDon');
        $NBan = $xml->createElement('NBan');
        $ndhdon->appendChild($NBan);

        $nmua = $xml->createElement('NMua');
        $nmua->appendChild($xml->createElement('Ten', $client['companyname'] ? $client['companyname'] : $client['clientname']));
        // $nmua->appendChild($xml->createElement('MKHang', $client['clientcode']));
        $nmua->appendChild($xml->createElement('HVTNMHang', $client['companyname'] ? $client['companyname'] : $client['clientname']));


        if ($client['taxid']) {
            $nmua->appendChild($xml->createElement('MST', $client['taxid']));
        }
        if ($client['email']) {
            $nmua->appendChild($xml->createElement('DCTDTu', $client['email']));
        }
        if ($client['nationalid']) {
            $nmua->appendChild($xml->createElement('CCCDan', $client['nationalid']));
        }
        if ($client['phonenumber']) {
            $nmua->appendChild($xml->createElement('SDThoai', $client['phonenumber']));
        }
        if ($client['address']) {
            $nmua->appendChild($xml->createElement('DChi', $client['address']));
        }

        $ndhdon->appendChild($nmua);

        $DSHHDVu = $xml->createElement('DSHHDVu');
        $i = 1;
        foreach ($items as $item) {
            $details = $xml->createElement('HHDVu');

            $TChat = $xml->createElement('TChat', '1');
            $details->appendChild($TChat);

            $stt = $xml->createElement('STT', "$i");
            $details->appendChild($stt);

            foreach ($item as $key => $value) {
                if ($key == 'id') {
                    continue;
                }
                $node = $xml->createElement($key, htmlspecialchars("$value"));
                $details->appendChild($node);
            }





            $DSHHDVu->appendChild($details);
            $i++;
        }
        $ndhdon->appendChild($DSHHDVu);

        $TToan = $xml->createElement('TToan');
        $THTTLTSuat = $xml->createElement('THTTLTSuat');
        $TToan->appendChild($THTTLTSuat);

        foreach ($taxtable as $itemls) {
            $lsdetails = $xml->createElement('LTSuat');
            foreach ($itemls as $key => $value) {
                $ls = $xml->createElement($key, "$value");
                $lsdetails->appendChild($ls);
            }
            $THTTLTSuat->appendChild($lsdetails);
        }

        $TgTCThue = $xml->createElement('TgTCThue', "" . $data['TgTCThue']);
        $TgTThue = $xml->createElement('TgTThue', "" . $data['TgTThue']);
        $TgTTTBSo = $xml->createElement('TgTTTBSo', "" . $data['TgTTTBSo']);
        $TgTTTBChu = $xml->createElement('TgTTTBChu', $this->sotienbangchu($data['TgTTTBSo']));
        // $TTCKTMai = $xml->createElement('TTCKTMai');
        $TToan->appendChild($TgTCThue);
        $TToan->appendChild($TgTThue);
        $TToan->appendChild($TgTTTBSo);
        $TToan->appendChild($TgTTTBChu);

        $ndhdon->appendChild($TToan);

        $dlhdon->appendChild($ndhdon);


        $hdon->appendChild($dlhdon);
        $dshdon->appendChild($hdon);


        $xml->appendChild($dshdon);

        return $xml->saveXML($xml->documentElement);
    }

    private function sotienbangchu($amount)
    {
        $amount = "$amount";
        if ($amount <= 0) {
            return $textnumber = "Tiền phải là số nguyên dương lớn hơn số 0";
        }
        $Text = array("không", "một", "hai", "ba", "bốn", "năm", "sáu", "bảy", "tám", "chín");
        $TextLuythua = array("", "nghìn", "triệu", "tỷ", "ngàn tỷ", "triệu tỷ", "tỷ tỷ");
        $textnumber = "";
        $length = strlen($amount);

        for ($i = 0; $i < $length; $i++)
            $unread[$i] = 0;

        for ($i = 0; $i < $length; $i++) {
            $so = substr($amount, $length - $i - 1, 1);

            if (($so == 0) && ($i % 3 == 0) && ($unread[$i] == 0)) {
                for ($j = $i + 1; $j < $length; $j++) {
                    $so1 = substr($amount, $length - $j - 1, 1);
                    if ($so1 != 0)
                        break;
                }

                if (intval(($j - $i) / 3) > 0) {
                    for ($k = $i; $k < intval(($j - $i) / 3) * 3 + $i; $k++)
                        $unread[$k] = 1;
                }
            }
        }

        for ($i = 0; $i < $length; $i++) {
            $so = substr($amount, $length - $i - 1, 1);
            if ($unread[$i] == 1)
                continue;

            if (($i % 3 == 0) && ($i > 0))
                $textnumber = $TextLuythua[$i / 3] . " " . $textnumber;

            if ($i % 3 == 2)
                $textnumber = 'trăm ' . $textnumber;

            if ($i % 3 == 1)
                $textnumber = 'mươi ' . $textnumber;


            $textnumber = $Text[$so] . " " . $textnumber;
        }

        //Phai de cac ham replace theo dung thu tu nhu the nay
        $textnumber = str_replace("không mươi", "lẻ", $textnumber);
        $textnumber = str_replace("lẻ không", "", $textnumber);
        $textnumber = str_replace("mươi không", "mươi", $textnumber);
        $textnumber = str_replace("một mươi", "mười", $textnumber);
        $textnumber = str_replace("mươi năm", "mươi lăm", $textnumber);
        $textnumber = str_replace("mươi một", "mươi mốt", $textnumber);
        $textnumber = str_replace("mười năm", "mười lăm", $textnumber);

        return ucfirst($textnumber . " đồng chẵn");
    }

    private function sendVNPTInvoice($url, $xml, $method = 'POST')
    {
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_POSTFIELDS => $xml,
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/soap+xml; charset=utf-8'
            ),
        ));
        $response = curl_exec($curl);
        curl_close($curl);
        $cleanxml = str_ireplace(['SOAP-ENV:', 'SOAP:'], '', $response);
        $cleanxml = str_ireplace('NS1:', '', $cleanxml);
        $result = simplexml_load_string($cleanxml);

        return $result;
    }
}

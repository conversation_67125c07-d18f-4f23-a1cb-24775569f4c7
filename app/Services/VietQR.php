<?php

namespace App\Services;

class VietQR
{
    public static function get_string($data)
    {
        $str = '************'; //mã khởi tạo qr code dùng 1 lần. dùng lại đổi 12 thành 11 cuối
        // ĐVCNTT String
        $DVCNTT_ID = "38";
        $AID_id = '00';
        $AID = 'A000000727';
        $AID_lenght = "10";
        $NHTV_id = "01";
        $bankcode_id = "00";
        // $bankcode = "970436";
        $bankcode_lenght = strlen($data['bank_id']);
        if ($bankcode_lenght < 10) {
            $bankcode_lenght = "0" . $bankcode_lenght;
        }
        $Merchant_id = "01";
        // $Merchant = "**********";
        $Merchant_lenght = strlen($data['account_no']);
        if ($Merchant_lenght < 10) {
            $Merchant_lenght = "0" . $Merchant_lenght;
        }
        $NHTV_lenght = strlen($bankcode_id . $bankcode_lenght . $data['bank_id'] . $Merchant_id . $Merchant_lenght . $data['account_no']);
        if ($NHTV_lenght < 10) {
            $NHTV_lenght = "0" . $NHTV_lenght;
        }
        $madichvu_id = "02";
        $madichvu = "QRIBFTTA"; //QRIBFTTA Qua stk, QRIBFTTC qua thẻ
        $madichvu_lenght = "08";
        $DVCNTT_lenght = strlen($AID_id . $AID_lenght . $AID . $NHTV_id . $NHTV_lenght . $bankcode_id . $bankcode_lenght . $data['bank_id'] . $Merchant_id . $Merchant_lenght . $data['account_no'] . $madichvu_id . $madichvu_lenght . $madichvu);
        if ($DVCNTT_lenght < 10) {
            $DVCNTT_lenght = "0" . $DVCNTT_lenght;
        }
        //Tien te
        $result_tiente = "5303704";
        if (isset($data['amount']) && !empty($data['amount'])) {
            $matiente_id = "53";
            $matiente = "704";
            $matiente_lenght = "03";
            //So tien
            $amount_id = "54";
            $amount = $data['amount'];
            $amount_lenght = strlen($amount);
            if ($amount_lenght < 10) {
                $amount_lenght = "0" . $amount_lenght;
            }
            $result_tiente = $matiente_id . $matiente_lenght . $matiente . $amount_id . $amount_lenght . $amount;
        }
        //Quoc gia
        $quocgia_id = "58";
        $quocgia = 'VN';
        $quocgia_lenght = "02";
        // Noi dung

        $result_memo = "";
        if (isset($data['memo']) && !empty($data['memo'])) {
            $noidung = vn_to_str($data['memo']);
            if (strlen($noidung) < 10) {
                $noidung = "080" . strlen($noidung) . $noidung;
            } else {
                $noidung = "08" . strlen($noidung) . $noidung;
            }
            $noidung_id = "62";
            $noidung_lenght =  strlen($noidung);
            if ($noidung_lenght < 10) {
                $noidung_lenght = "0" . $noidung_lenght;
            }

            $result_memo = $noidung_id . $noidung_lenght . $noidung;
        }

        //CRC
        $crc_id = "63";
        $crc_lenght = "04";
        $str = (string) $str . $DVCNTT_ID . $DVCNTT_lenght . $AID_id . $AID_lenght . $AID . $NHTV_id . $NHTV_lenght . $bankcode_id . $bankcode_lenght . $data['bank_id'] . $Merchant_id . $Merchant_lenght . $data['account_no'] . $madichvu_id . $madichvu_lenght . $madichvu . $result_tiente . $quocgia_id . $quocgia_lenght . $quocgia . $result_memo . $crc_id . $crc_lenght;
        $crc = self::crcChecksum($str);

        if (strlen($crc) == 3) {
            $crc = "0" . $crc;
        }
        if (strlen($crc) == 2) {
            $crc = "00" . $crc;
        }
        if (strlen($crc) == 1) {
            $crc = "000" . $crc;
        }

        $qr = (string) $str . $crc;

        return $qr;
    }

    public static function crcChecksum($str)
    {
        // The PHP version of the JS str.charCodeAt(i)

        $crc = 0xFFFF;
        $strlen = strlen($str);
        for ($c = 0; $c < $strlen; $c++) {
            $crc ^= self::charCodeAt($str, $c) << 8;
            for ($i = 0; $i < 8; $i++) {
                if ($crc & 0x8000) {
                    $crc = ($crc << 1) ^ 0x1021;
                } else {
                    $crc = $crc << 1;
                }
            }
        }
        $hex = $crc & 0xFFFF;
        $hex = dechex($hex);
        $hex = strtoupper($hex);
        return $hex;
    }
    private static function charCodeAt($str, $i)
    {
        return ord(substr($str, $i, 1));
    }
}

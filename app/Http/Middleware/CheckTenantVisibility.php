<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Stancl\Tenancy\Facades\Tenancy;

class CheckTenantVisibility
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $tenant = tenant();

        // Check if tenant exists
        if (!$tenant) {
            return $this->handleInvisibleTenant($request);
        }

        // Check if tenant is active/visible
        if (!$this->isTenantVisible($tenant)) {
            return $this->handleInvisibleTenant($request);
        }

        // // Check if tenant has access to web pages
        // if (!$this->canAccessWebPages($tenant)) {
        //     return $this->handleRestrictedAccess($request);
        // }

        // // Update last accessed timestamp
        // $this->updateLastAccessed($tenant);

        return $next($request);
    }

    /**
     * Check if tenant is visible/active
     */
    private function isTenantVisible($tenant): bool
    {
        // Use model method if available
        if (method_exists($tenant, 'isVisible')) {
            return $tenant->isVisible();
        }

        // Fallback to direct field checks
        if (isset($tenant->is_visible)) {
            return $tenant->is_visible === true || $tenant->is_visible === 1;
        }

        if (isset($tenant->status)) {
            return $tenant->status === 'active';
        }

        if (isset($tenant->is_active)) {
            return $tenant->is_active === true || $tenant->is_active === 1;
        }

        // If no status field, assume visible
        return true;
    }

    /**
     * Check if tenant can access web pages
     */
    private function canAccessWebPages($tenant): bool
    {
        // Use model method if available
        if (method_exists($tenant, 'canAccessWeb')) {
            return $tenant->canAccessWeb();
        }

        // Fallback to direct field checks
        if (isset($tenant->web_access)) {
            return $tenant->web_access === true || $tenant->web_access === 1;
        }

        if (isset($tenant->subscription_status)) {
            return in_array($tenant->subscription_status, ['active', 'trial']);
        }

        if (isset($tenant->is_suspended)) {
            return $tenant->is_suspended === false || $tenant->is_suspended === 0;
        }

        if (isset($tenant->expires_at)) {
            return $tenant->expires_at === null || now()->lt($tenant->expires_at);
        }

        // If no restrictions, allow access
        return true;
    }

    /**
     * Update tenant's last accessed timestamp
     */
    private function updateLastAccessed($tenant): void
    {
        try {
            if (method_exists($tenant, 'updateLastAccessed')) {
                $tenant->updateLastAccessed();
            } elseif (isset($tenant->last_accessed_at)) {
                $tenant->update(['last_accessed_at' => now()]);
            }
        } catch (\Exception $e) {
            // Silently fail to avoid breaking the request
            // Log the error if needed
        }
    }

    /**
     * Handle invisible/inactive tenant
     */
    private function handleInvisibleTenant(Request $request)
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'Tenant not found or inactive',
                'message' => 'The requested tenant is not available.'
            ], 404);
        }

        // Return a custom 404 page or redirect
        return response()->view('errors.tenant-not-found', [], 404);
    }

    /**
     * Handle restricted access (tenant exists but can't access web)
     */
    private function handleRestrictedAccess(Request $request)
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'Access restricted',
                'message' => 'Web access is not available for this tenant.'
            ], 403);
        }

        // Return a custom access restricted page
        return response()->view('errors.tenant-restricted', [], 403);
    }
}

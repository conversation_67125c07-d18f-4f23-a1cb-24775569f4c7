<?php

namespace App\Http\Controllers\Shopper\Report;

use App\Http\Controllers\Controller;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Shopper\Core\Models\Order;

class SaleReportPrintController extends Controller
{
    public function print(Request $request)
    {
        $period = $request->get('period', 'today');
        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');

        // If dates are provided, use them; otherwise calculate from period
        if ($startDate && $endDate) {
            $startDate = Carbon::parse($startDate)->startOfDay();
            $endDate = Carbon::parse($endDate)->endOfDay();
        } else {
            [$startDate, $endDate] = $this->getDateRangeFromPeriod($period);
        }

        $reportData = $this->getSalesData($startDate, $endDate);
        $totalSummary = $this->calculateTotalSummary($reportData);
        $periods = [
            'today' => 'Hôm nay',
            'tomorrow' => 'Ngày mai',
            'last_7_days' => '7 ngày qua',
            'last_month' => 'Tháng trước',
            'last_3_months' => '3 tháng qua',
        ];

        return view('shopper.pages.report.sale-report-print', compact(
            'reportData',
            'totalSummary',
            'startDate',
            'endDate',
            'period',
            'periods'
        ));
    }

    private function getDateRangeFromPeriod($period)
    {
        $now = Carbon::now();
        
        switch ($period) {
            case 'today':
                return [$now->copy()->startOfDay(), $now->copy()->endOfDay()];
                
            case 'tomorrow':
                return [$now->copy()->addDay()->startOfDay(), $now->copy()->addDay()->endOfDay()];
                
            case 'last_7_days':
                return [$now->copy()->subDays(7)->startOfDay(), $now->copy()->endOfDay()];
                
            case 'last_month':
                return [$now->copy()->subMonth()->startOfMonth(), $now->copy()->subMonth()->endOfMonth()];
                
            case 'last_3_months':
                return [$now->copy()->subMonths(3)->startOfMonth(), $now->copy()->endOfMonth()];
                
            default:
                return [$now->copy()->startOfDay(), $now->copy()->endOfDay()];
        }
    }

    private function getSalesData($startDate, $endDate)
    {
        $orders = Order::with(['items.product', 'paymentMethod'])
            ->whereBetween('created_at', [$startDate, $endDate])
            ->where('status', 'completed')
            ->get();

        $salesData = [];
        
        foreach ($orders as $order) {
            $date = $order->created_at->format('Y-m-d');
            
            if (!isset($salesData[$date])) {
                $salesData[$date] = [
                    'date' => $date,
                    'orders_count' => 0,
                    'total_sales' => 0,
                    'total_discount' => 0,
                    'total_tax' => 0,
                    'net_sales' => 0,
                    'orders' => [],
                ];
            }
            
            $salesData[$date]['orders_count']++;
            $salesData[$date]['total_sales'] += $order->total();
            $salesData[$date]['total_discount'] += $order->discount_amount ?? 0;
            $salesData[$date]['total_tax'] += $order->tax_amount ?? 0;
            $salesData[$date]['net_sales'] += ($order->total() - ($order->discount_amount ?? 0));
            
            // Add order details
            $salesData[$date]['orders'][] = [
                'id' => $order->id,
                'number' => $order->number ?? '#' . $order->id,
                'total' => $order->total(),
                'discount' => $order->discount_amount ?? 0,
                'tax' => $order->tax_amount ?? 0,
                'payment_method' => $order->paymentMethod->title ?? 'Unknown',
                'created_at' => $order->created_at,
                'items_count' => $order->items->count(),
            ];
        }
        
        // Sort orders by creation time (newest first) for each date
        foreach ($salesData as &$dayData) {
            usort($dayData['orders'], function ($a, $b) {
                return $b['created_at'] <=> $a['created_at'];
            });
        }
        
        return array_values($salesData);
    }

    private function calculateTotalSummary($reportData)
    {
        $summary = [
            'total_orders' => 0,
            'total_sales' => 0,
            'total_discount' => 0,
            'total_tax' => 0,
            'net_sales' => 0,
            'average_order_value' => 0,
        ];
        
        foreach ($reportData as $dayData) {
            $summary['total_orders'] += $dayData['orders_count'];
            $summary['total_sales'] += $dayData['total_sales'];
            $summary['total_discount'] += $dayData['total_discount'];
            $summary['total_tax'] += $dayData['total_tax'];
            $summary['net_sales'] += $dayData['net_sales'];
        }
        
        if ($summary['total_orders'] > 0) {
            $summary['average_order_value'] = $summary['total_sales'] / $summary['total_orders'];
        }
        
        return $summary;
    }
}

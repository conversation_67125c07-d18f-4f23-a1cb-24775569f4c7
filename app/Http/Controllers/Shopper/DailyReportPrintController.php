<?php

namespace App\Http\Controllers\Shopper;

use App\Http\Controllers\Controller;
use App\Livewire\Shopper\Pages\Report\DailyReport;
use Illuminate\Http\Request;

class DailyReportPrintController extends Controller
{
    public function printReport(Request $request)
    {
        $dailyReport = new DailyReport();
        $dailyReport->selectedDate = $request->get('date', now()->format('Y-m-d'));
        $dailyReport->generateReport();

        $printData = [
            'selectedDate' => $dailyReport->selectedDate,
            'reportData' => $dailyReport->reportData,
            'totalSummary' => $dailyReport->totalSummary,
            'generatedAt' => now()->format('Y-m-d H:i:s'),
            'generatedBy' => auth()->user()->first_name . ' ' . auth()->user()->last_name,
        ];

        return view('livewire.shopper.pages.report.daily-report-print', $printData);
    }

    public function printSummaryOnly(Request $request)
    {
        $dailyReport = new DailyReport();
        $dailyReport->selectedDate = $request->get('date', now()->format('Y-m-d'));
        $dailyReport->generateReport();

        $printData = [
            'selectedDate' => $dailyReport->selectedDate,
            'totalSummary' => $dailyReport->totalSummary,
            'reportData' => $dailyReport->reportData,
            'summaryOnly' => true,
            'generatedAt' => now()->format('Y-m-d H:i:s'),
            'generatedBy' => auth()->user()->first_name . ' ' . auth()->user()->last_name,
        ];

        return view('livewire.shopper.pages.report.daily-report-print', $printData);
    }

    public function printStaffReport(Request $request)
    {
        $dailyReport = new DailyReport();
        $dailyReport->selectedDate = $request->get('date', now()->format('Y-m-d'));
        $dailyReport->generateReport();

        $staffId = $request->get('staff_id');
        $staffData = collect($dailyReport->reportData)->where('staff_id', $staffId);

        if (!$staffData) {
            return view('livewire.shopper.pages.report.daily-report-print', [
                'selectedDate' => $dailyReport->selectedDate,
                'error' => 'Staff report not found.',
                'generatedAt' => now()->format('Y-m-d H:i:s'),
                'generatedBy' => auth()->user()->first_name . ' ' . auth()->user()->last_name,
            ]);
        }

        $printData = [
            'selectedDate' => $dailyReport->selectedDate,
            'reportData' => $staffData,
            'staffOnly' => true,
            'generatedAt' => now()->format('Y-m-d H:i:s'),
            'generatedBy' => auth()->user()->first_name . ' ' . auth()->user()->last_name,
        ];

        return view('livewire.shopper.pages.report.daily-report-print', $printData);
    }
}

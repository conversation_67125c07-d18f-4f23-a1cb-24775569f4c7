<?php

declare(strict_types=1);

namespace App\Traits;

use App\Models\Tax;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

trait HasTaxes
{
    /**
     * Get all taxes applied to this model
     */
    public function taxes(): MorphToMany
    {
        return $this->morphToMany(
            Tax::class,
            'taxable',
            shopper_table('product_taxes')
        )->withPivot(['is_active', 'custom_rate', 'custom_fixed_amount', 'metadata'])
         ->withTimestamps();
    }

    /**
     * Get only active taxes applied to this model
     */
    public function activeTaxes(): MorphToMany
    {
        return $this->taxes()
            ->where('taxes.is_active', true)
            ->wherePivot('is_active', true);
    }

    /**
     * Get taxes ordered by priority for calculation
     */
    public function orderedTaxes(): MorphToMany
    {
        return $this->activeTaxes()->orderBy('priority', 'asc');
    }

    /**
     * Check if this model has any taxes applied
     */
    public function hasTaxes(): bool
    {
        return $this->activeTaxes()->exists();
    }

    /**
     * Get applicable tax rates for a specific zone and currency
     */
    public function getApplicableTaxRates(?int $zoneId = null, ?int $currencyId = null): array
    {
        $taxes = $this->orderedTaxes()->get();
        $taxRates = [];

        foreach ($taxes as $tax) {
            $taxRate = $tax->getTaxRateForZone($zoneId, $currencyId);
            
            if ($taxRate) {
                // Check if there's a custom rate for this product
                $customRate = $tax->pivot->custom_rate;
                $customFixedAmount = $tax->pivot->custom_fixed_amount;
                
                $taxRates[] = [
                    'tax' => $tax,
                    'tax_rate' => $taxRate,
                    'custom_rate' => $customRate,
                    'custom_fixed_amount' => $customFixedAmount,
                    'effective_rate' => $customRate ?? $taxRate->rate,
                    'effective_fixed_amount' => $customFixedAmount ?? $taxRate->fixed_amount,
                ];
            }
        }

        return $taxRates;
    }

    /**
     * Calculate total tax amount for a given base amount
     */
    public function calculateTaxAmount(int $baseAmount, ?int $zoneId = null, ?int $currencyId = null): array
    {
        $taxRates = $this->getApplicableTaxRates($zoneId, $currencyId);
        $taxBreakdown = [];
        $totalTaxAmount = 0;
        $compoundBase = $baseAmount;

        foreach ($taxRates as $taxData) {
            $tax = $taxData['tax'];
            $taxAmount = 0;

            if ($tax->isFixedAmount()) {
                $taxAmount = $taxData['effective_fixed_amount'] ?? 0;
            } else {
                $rate = $taxData['effective_rate'] ?? 0;
                $calculationBase = $tax->is_compound ? $compoundBase : $baseAmount;
                $taxAmount = (int) round($calculationBase * ($rate / 100));
            }

            $taxBreakdown[] = [
                'tax_id' => $tax->id,
                'tax_name' => $tax->name,
                'tax_code' => $tax->code,
                'tax_rate' => $taxData['effective_rate'] ?? 0,
                'tax_amount' => $taxAmount,
                'taxable_amount' => $tax->is_compound ? $compoundBase : $baseAmount,
                'is_inclusive' => $tax->is_inclusive,
                'is_compound' => $tax->is_compound,
            ];

            $totalTaxAmount += $taxAmount;
            
            // For compound taxes, add this tax to the base for next calculations
            if ($tax->is_compound) {
                $compoundBase += $taxAmount;
            }
        }

        return [
            'total_tax_amount' => $totalTaxAmount,
            'tax_breakdown' => $taxBreakdown,
        ];
    }

    /**
     * Get price including taxes
     */
    public function getPriceWithTaxes(int $baseAmount, ?int $zoneId = null, ?int $currencyId = null): array
    {
        $taxCalculation = $this->calculateTaxAmount($baseAmount, $zoneId, $currencyId);
        
        return [
            'base_amount' => $baseAmount,
            'tax_amount' => $taxCalculation['total_tax_amount'],
            'total_amount' => $baseAmount + $taxCalculation['total_tax_amount'],
            'tax_breakdown' => $taxCalculation['tax_breakdown'],
        ];
    }

    /**
     * Attach a tax to this model
     */
    public function attachTax(int $taxId, array $attributes = []): void
    {
        $this->taxes()->attach($taxId, array_merge([
            'is_active' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ], $attributes));
    }

    /**
     * Detach a tax from this model
     */
    public function detachTax(int $taxId): void
    {
        $this->taxes()->detach($taxId);
    }

    /**
     * Sync taxes for this model
     */
    public function syncTaxes(array $taxIds): void
    {
        $syncData = [];
        foreach ($taxIds as $taxId => $attributes) {
            if (is_numeric($taxId) && is_array($attributes)) {
                $syncData[$taxId] = array_merge([
                    'is_active' => true,
                    'created_at' => now(),
                    'updated_at' => now(),
                ], $attributes);
            } elseif (is_numeric($attributes)) {
                $syncData[$attributes] = [
                    'is_active' => true,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }
        }
        
        $this->taxes()->sync($syncData);
    }
}

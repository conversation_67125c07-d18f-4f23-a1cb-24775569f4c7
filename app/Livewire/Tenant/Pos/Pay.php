<?php

namespace App\Livewire\Tenant\Pos;

use App\Actions\CreateOrder;
use App\Models\Bank;
use App\Models\User;
use App\Services\VietQR;
use Darryldecode\Cart\Facades\CartFacade;
use Endroid\QrCode\Color\Color;
use Endroid\QrCode\Encoding\Encoding;
use Endroid\QrCode\ErrorCorrectionLevel;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\RoundBlockSizeMode;
use Endroid\QrCode\Writer\PngWriter;
use Flux\Flux;
use Illuminate\Support\Facades\Session;
use Livewire\Attributes\On;
use Livewire\Component;
use Shopper\Core\Enum\AddressType;
use Shopper\Core\Enum\OrderStatus;
use Shopper\Core\Models\Country;
use Shopper\Core\Models\OrderAddress;
use Shopper\Core\Models\PaymentMethod;

class Pay extends Component
{
    public $sessionKey;
    public $cartItems;
    public $subTotal;
    public $total;
    public $payments;
    public $customerId;
    public $payment_method_id = 1;
    public $is_export_invoice = false;
    public $is_print_bill = true;
    public $currentOrderId = null;
    public $totalTax = 0;
    public $taxBreakdown = [];

    public function clearCart()
    {
        CartFacade::session(Session::getId())->clear();
        $this->currentOrderId = null; // Reset current order ID when clearing cart
        $this->dispatch('cartUpdated');
    }

    public function saveDraft()
    {
        if (CartFacade::session(Session::getId())->isEmpty()) {
            Flux::toast('Giỏ hàng trống', variant: 'danger');
            return;
        }

        $data = [
            'payment_method_id' => null,
            'export_invoice' => $this->export_invoice ?? false,
            'status' => OrderStatus::Pending,
        ];

        if ($this->currentOrderId) {
            // Update existing draft order
            $order = \Shopper\Core\Models\Order::find($this->currentOrderId);

            if ($order && $order->status === OrderStatus::Pending) {
                // Delete existing order items
                $order->items()->delete();

                // Update order details
                $order->update([
                    'payment_method_id' => $data['payment_method_id'],
                    'status' => $data['status'],
                ]);

                // Add new items from cart
                $sessionId = Session::getId();
                foreach (CartFacade::session($sessionId)->getContent() as $item) {
                    \Shopper\Core\Models\OrderItem::create([
                        'order_id' => $order->id,
                        'quantity' => $item->quantity,
                        'unit_price_amount' => $item->price,
                        'name' => $item->name,
                        'sku' => $item->associatedModel->sku,
                        'product_id' => $item->id,
                        'product_type' => $item->associatedModel->getMorphClass(),
                    ]);
                }

                CartFacade::session(Session::getId())->clear(); // @phpstan-ignore-line
                Flux::toast('Đơn hàng nháp đã được cập nhật', variant: 'success');
                $this->currentOrderId = null; // Reset current order ID
            } else {
                // Order not found or not a draft, create new one
                $order = (new CreateOrder)->handle($data);
                CartFacade::session(Session::getId())->clear(); // @phpstan-ignore-line
                Flux::toast('Đơn hàng đã được lưu nháp', variant: 'success');
                $this->currentOrderId = null; // Reset current order ID
            }
        } else {
            // Create new draft order
            $order = (new CreateOrder)->handle($data);
            CartFacade::session(Session::getId())->clear(); // @phpstan-ignore-line
            Flux::toast('Đơn hàng đã được lưu nháp', variant: 'success');
        }

        $this->dispatch('cartUpdated');
        $this->dispatch('pendingOrdersUpdated'); // Dispatch event to update pending orders count
    }

    public function printTemp()
    {
        if (CartFacade::session(Session::getId())->isEmpty()) {
            Flux::toast('Giỏ hàng trống', variant: 'danger');
            return;
        }

        // Get cart items
        $items = CartFacade::session(Session::getId())->getContent();
        $subTotal = CartFacade::session(Session::getId())->getSubTotal();
        $total = CartFacade::session(Session::getId())->getTotal();

        // Pass data to the print view
        $printData = [
            'items' => $items,
            'subTotal' => $this->subTotal,
            'total' => $this->total,
            'totalTax' => $this->totalTax,
            'date' => now()->format('d/m/Y H:i:s'),
            'store' => [
                'name' => shopper_setting('name'),
                'address' => 'Điạ chỉ: ' . shopper_setting('street_address') . ', ' . shopper_setting('city'),
                'phone' => shopper_setting('phone_number'),
            ]
        ];

        // Dispatch a browser event to trigger printing
        $this->dispatch('print-bill', $printData);
    }

    public function checkout()
    {
        if (CartFacade::session(Session::getId())->isEmpty()) {
            Flux::toast('Giỏ hàng trống', variant: 'danger');
            return;
        }

        $validated = $this->validate([
            'customerId' => 'required',
            'payment_method_id' => 'required',
            'is_export_invoice' => 'nullable|boolean',
            'is_print_bill' => 'nullable|boolean',
        ]);

        $customer = User::query()->find($validated['customerId']);
        $customerShippingAddress = $customer->addresses()->where('type', AddressType::Shipping)->first();
        $orderShippingAddress = null;

        if ($customerShippingAddress) {
            $orderShippingAddress = OrderAddress::query()->create([
                'customer_id' => $validated['customerId'],
                'last_name' => $customerShippingAddress->last_name,
                'first_name' => $customerShippingAddress->first_name,
                'street_address' => $customerShippingAddress->street_address,
                'street_address_plus' => $customerShippingAddress->street_address_plus,
                'city' => $customerShippingAddress->city,
                'postal_code' => $customerShippingAddress->postal_code,
                'phone' => $customerShippingAddress->phone_number,
                'country_name' => Country::query()
                    ->find($customerShippingAddress->country_id)
                    ->name,
            ]);
        }

        $data = [
            'customerId' => $validated['customerId'],
            'payment_method_id' => $validated['payment_method_id'],
            'is_export_invoice' => $validated['is_export_invoice'] ?? false,
            'status' => OrderStatus::Completed,
            'shipping_address_id' => $orderShippingAddress ? $orderShippingAddress->id : null,
        ];

        // Check if we're completing a draft order
        if ($this->currentOrderId) {
            $order = \Shopper\Core\Models\Order::find($this->currentOrderId);

            if ($order && $order->status === OrderStatus::Pending) {
                // Delete existing order items
                $order->items()->delete();
                $order->delete();
                $order = (new CreateOrder)->handle($data);
            } else {
                // Order not found or not a draft, create new one
                $order = (new CreateOrder)->handle($data);
            }
        } else {
            // Create new order
            $order = (new CreateOrder)->handle($data);
        }

        $payment = PaymentMethod::query()->find($validated['payment_method_id']);

        // Prepare data for completed order modal
        $orderData = [
            'order_number' => $order->number,
            'order_date' => $order->created_at->format('d/m/Y H:i:s'),
            'customer_name' => $customer->first_name . ' ' . $customer->last_name,
            'customer_phone' => $customer->phone_number,
            'total' => $this->total,
            'payment_method' => $payment->title,
            'qrImage' => null,
            'bank' => null,
        ];

        // Generate QR code for bank transfer if applicable
        if ($payment->is_bank_transfer) {
            $bank = Bank::find($payment->bank_id);
            $qrString = $this->generateQR($payment, $this->total, $order->number);
            $qrCode = new QrCode(
                data: $qrString,
                encoding: new Encoding('UTF-8'),
                errorCorrectionLevel: ErrorCorrectionLevel::Low,
                size: 450,
                margin: 10,
                roundBlockSizeMode: RoundBlockSizeMode::Margin,
                foregroundColor: new Color(0, 0, 0),
                backgroundColor: new Color(255, 255, 255)
            );
            $writer = new PngWriter();
            $resultQr = $writer->write($qrCode);

            $orderData['qrImage'] = $resultQr->getDataUri();
            $orderData['bank'] = [
                'shortName' => $bank->shortName,
                'account_name' => $payment->bank_account_name,
                'account_number' => $payment->bank_account_number,
                'memo' => $order->number,
            ];
        }

        if ($this->is_print_bill) {
            // Get cart items
            $items = CartFacade::session(Session::getId())->getContent();

            // Pass data to the print view
            $printData = [
                'items' => $items,
                'subTotal' => $this->subTotal,
                'total' => $this->total,
                'totalTax' => $this->totalTax,
                'date' => now()->format('d/m/Y H:i:s'),
                'customer' => $customer->first_name . ' ' . $customer->last_name . ' - ' . $customer->phone_number,
                'store' => [
                    'name' => shopper_setting('name'),
                    'address' => 'Điạ chỉ: ' . shopper_setting('street_address') . ', ' . shopper_setting('city'),
                    'phone' => shopper_setting('phone_number'),
                ],
                'payment' => $payment->toArray(),
                'qrImage' => $orderData['qrImage'],
                'bank' => $orderData['bank'],
                'memo' => $order->number,
                'order_number' => $order->number,
            ];
            $this->dispatch('print-bill-final', $printData);
        }

        CartFacade::session(Session::getId())->clear(); // @phpstan-ignore-line
        Flux::toast('Đơn hàng đã được hoàn thành', variant: 'success');
        Flux::modal('payment')->close();

        // Pass order data to the completed order modal
        $this->dispatch('order-completed', $orderData);
        Flux::modal('order-completed-modal')->show();
        $this->dispatch('cartUpdated');
        $this->dispatch('pendingOrdersUpdated'); // Dispatch event to update pending orders count
        $this->currentOrderId = null; // Reset current order ID after checkout
    }

    public function generateQR($payment, $amount = 0, $memo = '')
    {
        if ($payment->is_bank_transfer) {
            $bank = Bank::find($payment->bank_id);
            $dataPost = [
                "bank_id" => $bank->bin,
                "account_no" => $payment->bank_account_number,
                "amount" => $amount,
                "memo" => $memo,
            ];

            $rs = VietQR::get_string($dataPost);
            return $rs;
        }

        return false;
    }
    public function mount()
    {
        $this->sessionKey = Session::getId();
        $this->cartItems = CartFacade::session($this->sessionKey)->getContent()->values();
        $this->subTotal = CartFacade::session($this->sessionKey)->getSubTotal();
        $this->total = CartFacade::session($this->sessionKey)->getTotal();
        $this->payments = PaymentMethod::enabled()->get();
        $this->calculateCartTaxes();
    }


    #[On('cartUpdated')]
    public function cartUpdated(): void
    {
        $this->cartItems = CartFacade::session($this->sessionKey)->getContent()->values();
        $this->subTotal = CartFacade::session($this->sessionKey)->getSubTotal();
        $this->total = CartFacade::session($this->sessionKey)->getTotal();
        $this->calculateCartTaxes();
    }

    #[On('customerIdUpdated')]
    public function customerIdUpdated($customerId)
    {
        $this->customerId = $customerId;
    }

    #[On('customerCreated')]
    public function customerCreated($customer)
    {
        $this->customerId = $customer['id'];
    }

    #[On('orderDraftLoaded')]
    public function orderDraftLoaded($orderId)
    {
        $this->currentOrderId = $orderId;
    }

    protected function calculateCartTaxes(): void
    {
        $this->totalTax = 0;
        $this->taxBreakdown = [];

        foreach ($this->cartItems as $item) {
            if (isset($item->attributes['tax_amount']) && isset($item->attributes['tax_breakdown'])) {
                // Calculate total tax for this item (tax per unit * quantity)
                $itemTotalTax = $item->attributes['tax_amount'] * $item->quantity;
                $this->totalTax += $itemTotalTax;

                // Aggregate tax breakdown
                foreach ($item->attributes['tax_breakdown'] as $tax) {
                    $taxCode = $tax['tax_code'];
                    $taxAmountForQuantity = $tax['tax_amount'] * $item->quantity;

                    if (!isset($this->taxBreakdown[$taxCode])) {
                        $this->taxBreakdown[$taxCode] = [
                            'tax_name' => $tax['tax_name'],
                            'tax_code' => $taxCode,
                            'tax_rate' => $tax['tax_rate'],
                            'total_amount' => 0,
                        ];
                    }

                    $this->taxBreakdown[$taxCode]['total_amount'] += $taxAmountForQuantity;
                }
            }
        }

        // Update total to include taxes
        $this->total = $this->subTotal + $this->totalTax;
    }

    public function render()
    {
        return view('livewire.tenant.pos.pay');
    }
}

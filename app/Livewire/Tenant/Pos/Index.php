<?php

namespace App\Livewire\Tenant\Pos;

use App\Models\Product;
use App\Models\User;
use App\Services\TaxService;
use Darryldecode\Cart\Facades\CartFacade;
use Flux\Flux;
use Illuminate\Support\Facades\Session;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Component;
use Shopper\Core\Enum\OrderStatus;
use Shopper\Core\Models\Category;
use Shopper\Core\Models\Order;
use Shopper\Core\Models\OrderItem;

class Index extends Component
{
    public $categories;
    public $products;
    public $selectedCategory = 'all';
    public $searchProduct = '';
    public ?string $sessionKey = null;

    public $cartItems;
    public $subTotal;
    public $total;
    public $totalTax;
    public $taxBreakdown = [];

    public $customers;
    public $customerId = 2;
    public $searchCustomer = '';

    public $pendingOrders = [];
    public $showPendingOrders = false;

    public $cartItemQuantity = [];

    public function mount()
    {
        $this->categories = Category::all();
        $this->products = Product::whereHas('prices')->get();

        $this->customers = User::query()->get();

        $sessionKey = Session::getId();

        $this->sessionKey = $sessionKey;
        $this->cartItems = CartFacade::session($this->sessionKey)->getContent()->sortBy("attributes.added_at")->values();
        $this->subTotal = CartFacade::session($this->sessionKey)->getSubTotal();
        $this->total = CartFacade::session($this->sessionKey)->getTotal();

        // Calculate taxes for cart
        $this->calculateCartTaxes();

        // Initialize cartItemQuantity with current quantities
        foreach ($this->cartItems as $item) {
            $this->cartItemQuantity[$item->id] = $item->quantity;
        }

        // Load pending orders
        $this->loadPendingOrders();
    }

    public function loadPendingOrders()
    {
        $this->pendingOrders = Order::where('status', OrderStatus::Pending)
            ->with(['items', 'customer'])
            ->latest()
            ->get();
    }

    public function loadOrderToCart($orderId)
    {
        // Clear current cart
        CartFacade::session($this->sessionKey)->clear();

        // Get the order
        $order = Order::with(['items.product', 'customer'])->findOrFail($orderId);

        // Set customer
        if ($order->customer) {
            $this->customerId = $order->customer->id;
            $this->dispatch('customerIdUpdated', $order->customer->id);
        }

        // Add items to cart
        foreach ($order->items as $item) {
            if ($item->product) {
                // Calculate taxes for this product
                $basePrice = $item->unit_price_amount;
                $taxCalculation = $item->product->calculateTaxAmount($basePrice);

                CartFacade::session($this->sessionKey)->add([
                    'id' => $item->product->id,
                    'name' => $item->name,
                    'price' => $basePrice,
                    'quantity' => $item->quantity,
                    'attributes' => [
                        'added_at' => now(),
                        'tax_amount' => $taxCalculation['total_tax_amount'],
                        'tax_breakdown' => $taxCalculation['tax_breakdown'],
                        'price_with_tax' => $basePrice + $taxCalculation['total_tax_amount'],
                    ],
                    'associatedModel' => $item->product,
                ]);
            }
        }

        // Update cart
        $this->dispatch('cartUpdated');

        // Notify Pay component that we're editing a draft order
        $this->dispatch('orderDraftLoaded', $orderId);

        // Close pending orders panel
        $this->showPendingOrders = false;

        // Show success message
        Flux::modal('orders-draft')->close();
        Flux::toast('Đơn hàng nháp đã được tải vào giỏ hàng', variant: 'success');
    }

    public function addToCart($productId)
    {
        $product = Product::find($productId);

        // Calculate taxes for this product
        $basePrice = $product->getPrice()->amount->amount;
        $taxCalculation = $product->calculateTaxAmount($basePrice);

        CartFacade::session(Session::getId())->add([
            'id' => $product->id,
            'name' => $product->name,
            'price' => $basePrice,
            'quantity' => 1,
            'attributes' => [
                'added_at' => now(),
                'tax_amount' => $taxCalculation['total_tax_amount'],
                'tax_breakdown' => $taxCalculation['tax_breakdown'],
                'price_with_tax' => $basePrice + $taxCalculation['total_tax_amount'],
            ],
            'associatedModel' => $product,
        ]);
        $this->dispatch('cartUpdated');
    }

    public function incrementQuantity($cartItemId)
    {
        CartFacade::session($this->sessionKey)->update($cartItemId, [
            'quantity' =>  1,
        ]);
        $this->dispatch('cartUpdated');
    }

    public function decrementQuantity($cartItemId)
    {
        CartFacade::session($this->sessionKey)->update($cartItemId, [
            'quantity' => -1,
        ]);
        $this->dispatch('cartUpdated');
    }

    public function removeToCart($cartItemId)
    {
        CartFacade::session($this->sessionKey)->remove($cartItemId);
        $this->dispatch('cartUpdated');
    }

    public function updateQuantity($cartItemId, $quantity)
    {
        $quantity = (int) $quantity;

        // Ensure quantity is at least 1
        if ($quantity < 1) {
            $quantity = 1;
        }

        // Update cart item quantity directly to the specified value
        CartFacade::session($this->sessionKey)->update($cartItemId, [
            'quantity' => [
                'relative' => false,
                'value' => $quantity
            ],
        ]);

        $this->dispatch('cartUpdated');
    }

    #[On('cartUpdated')]
    public function cartUpdated(): void
    {
        $this->cartItems = CartFacade::session($this->sessionKey)->getContent()->sortBy("attributes.added_at")->values();
        $this->subTotal = CartFacade::session($this->sessionKey)->getSubTotal();
        $this->total = CartFacade::session($this->sessionKey)->getTotal();

        // Update cartItemQuantity with current quantities
        foreach ($this->cartItems as $item) {
            $this->cartItemQuantity[$item->id] = $item->quantity;
        }

        // Recalculate taxes for the entire cart
        $this->calculateCartTaxes();
    }

    public function updatedSelectedCategory()
    {
        $this->searchProduct = '';
        if ($this->selectedCategory == 'all') {
            $this->products = Product::scopes('publish')->get();
        } else {
            $this->products = Product::with('media', 'categories')
                ->scopes('publish')
                ->whereHas('prices')
                ->whereHas('categories', function ($query): void {
                    $query->where('id', $this->selectedCategory);
                })
                ->get();
        }
    }

    public function updatedSearchProduct()
    {
        if ($this->selectedCategory == 'all') {
            $this->products = Product::scopes('publish')->whereHas('prices')->where('name', 'LIKE', '%' . $this->searchProduct . '%')->get();
        } else {
            $this->products = Product::with('media', 'categories')
                ->scopes('publish')
                ->whereHas('prices')
                ->whereHas('categories', function ($query): void {
                    $query->where('id', $this->selectedCategory);
                })
                ->where('name', 'LIKE', '%' . $this->searchProduct . '%')
                ->get();
        }
    }

    public function updatedCustomerId()
    {
        $this->dispatch('customerIdUpdated', customerId: $this->customerId);
    }

    #[On('customerCreated')]
    public function customerCreated($customer)
    {
        $this->customers = User::query()->get();
        $this->customerId = $customer['id'];
    }

    #[On('pendingOrdersUpdated')]
    public function reloadPendingOrders()
    {
        $this->loadPendingOrders();
    }

    public function resetFilters()
    {
        $this->searchProduct = '';
        $this->selectedCategory = 'all';
        $this->products = Product::whereHas('prices')->get();
    }

    protected function calculateCartTaxes(): void
    {
        $this->totalTax = 0;
        $this->taxBreakdown = [];

        foreach ($this->cartItems as $item) {
            if (isset($item->attributes['tax_amount']) && isset($item->attributes['tax_breakdown'])) {
                // Calculate total tax for this item (tax per unit * quantity)
                $itemTotalTax = $item->attributes['tax_amount'] * $item->quantity;
                $this->totalTax += $itemTotalTax;

                // Aggregate tax breakdown
                foreach ($item->attributes['tax_breakdown'] as $tax) {
                    $taxCode = $tax['tax_code'];
                    $taxAmountForQuantity = $tax['tax_amount'] * $item->quantity;

                    if (!isset($this->taxBreakdown[$taxCode])) {
                        $this->taxBreakdown[$taxCode] = [
                            'tax_name' => $tax['tax_name'],
                            'tax_code' => $taxCode,
                            'tax_rate' => $tax['tax_rate'],
                            'total_amount' => 0,
                        ];
                    }

                    $this->taxBreakdown[$taxCode]['total_amount'] += $taxAmountForQuantity;
                }
            }
        }

        // Update total to include taxes
        $this->total = $this->subTotal + $this->totalTax;
    }

    public function getCartItemTaxAmount($item): int
    {
        if (isset($item->attributes['tax_amount'])) {
            return $item->attributes['tax_amount'] * $item->quantity;
        }
        return 0;
    }

    public function getCartItemPriceWithTax($item): int
    {
        $basePrice = $item->price * $item->quantity;
        $taxAmount = $this->getCartItemTaxAmount($item);
        return $basePrice + $taxAmount;
    }

    public function getFormattedTaxAmount($amount): string
    {
        return shopper_money_format($amount);
    }

    public function render()
    {
        return view('livewire.tenant.pos.index');
    }
}

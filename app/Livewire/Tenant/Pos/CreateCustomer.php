<?php

namespace App\Livewire\Tenant\Pos;

use App\Models\User;
use Flux\Flux;
use Livewire\Component;
use Shopper\Core\Enum\AddressType;

class CreateCustomer extends Component
{
    public $first_name;
    public $last_name;
    public $phone_number;
    public $email;

    public function createCustomer()
    {
        $validated = $this->validate([
            'first_name' => 'required',
            'last_name' => 'required',
            'phone_number' => 'required',
            'email' => 'required|email|unique:users',
        ]);

        $customer = User::query()->create([
            'first_name' => $validated['first_name'],
            'last_name' => $validated['last_name'],
            'phone_number' => $validated['phone_number'],
            'email' => $validated['email'],
        ]);
        $customer->assignRole(config('shopper.core.users.default_role'));
        $address = [
            'first_name' => $validated['first_name'],
            'last_name' => $validated['last_name'],
            'type' => AddressType::Shipping,
            'street_address' => 'Chưa xác định',
            'postal_code' => '000000',
            'city' => 'Chưa xác định',
            'phone_number' => $validated['phone_number'],
            'country_id' => 243,
            'user_id' => $customer->id,
        ];
        $customer->addresses()->create($address);
        $this->reset('first_name', 'last_name', 'phone_number', 'email');

        Flux::toast('Khách hàng đã được tạo', variant: 'success');
        Flux::modal('create-customer')->close();
        $this->dispatch('customerCreated', customer: $customer);
    }

    public function render()
    {
        return view('livewire.tenant.pos.create-customer');
    }
}

<?php

declare(strict_types=1);

namespace App\Livewire\Shopper\Pages;

use App\Models\User;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\DB;
use Shopper\Core\Enum\OrderStatus;
use Shopper\Core\Models\Order;
use Shopper\Core\Models\Product;
use Shopper\Livewire\Pages\AbstractPageComponent;
use Filament\Tables\Table;
use Filament\Tables;


class Dashboard extends AbstractPageComponent implements HasForms, HasTable
{
    use InteractsWithForms;
    use InteractsWithTable;
    public function render(): View
    {
        return view('livewire.shopper.pages.dashboard', [
            'totalOrders' => $this->getTotalOrders(),
            'totalCustomers' => $this->getTotalCustomers(),
            'totalProducts' => $this->getTotalProducts(),
            'recentOrders' => $this->getRecentOrders(),
        ]);
    }

    protected function getTotalOrders(): int
    {
        return Order::where('status', OrderStatus::Completed)->count();
    }

    protected function getTotalCustomers(): int
    {
        return User::customers()->count();
    }

    protected function getTotalProducts(): int
    {
        return Product::count();
    }

    protected function getRecentOrders()
    {
        return Order::with('customer')
            ->latest()
            ->limit(5)
            ->get();
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(
                Order::query()
                    ->with([
                        'customer',
                        'items',
                        'zone',
                        'items.product',
                        'items.product.media',
                    ])->latest()
            )
            ->columns([
                Tables\Columns\TextColumn::make('number')
                    ->label('Mã đơn hàng')
                    ->searchable()
                    ->extraAttributes(['class' => 'uppercase']),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('shopper::words.date'))
                    ->date()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('status')
                    ->label(__('shopper::forms.label.status'))
                    ->badge(),
                Tables\Columns\TextColumn::make('customer.first_name')
                    ->label(__('shopper::words.customer'))
                    ->searchable()
                    ->formatStateUsing(fn (Order $record): View => view(
                        'shopper::livewire.tables.cells.orders.customer',
                        ['order' => $record]
                    ))
                    ->toggleable(),
                Tables\Columns\TextColumn::make('id')
                    ->label(__('shopper::words.purchased'))
                    ->formatStateUsing(fn (Order $record): View => view(
                        'shopper::livewire.tables.cells.orders.purchased',
                        ['order' => $record]
                    )),
                Tables\Columns\TextColumn::make('currency_code')
                    ->label(__('shopper::forms.label.price_amount'))
                    ->formatStateUsing(
                        fn ($state, Order $record): string => shopper_money_format(amount: $record->total(), currency: $state)
                    ),
            ])
            ->actions([
                Tables\Actions\Action::make('view')
                    ->label(__('shopper::words.details'))
                    ->url(
                        fn (Order $record): string => route(
                            name: 'shopper.orders.detail',
                            parameters: ['order' => $record]
                        ),
                    ),
            ])
            ->heading('Đơn hàng gần đây')
            ->paginated([5, 10, 25, 50, 100, 'all'])
            ->defaultPaginationPageOption(5)
            ->deferLoading()
            ->emptyStateHeading('Không có đơn hàng nào.');
    }
}

<?php

namespace App\Livewire\Shopper\Pages\Report;

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Shopper\Core\Enum\OrderStatus;
use Shopper\Core\Models\Order;
use Shopper\Core\Models\OrderRefund;
use Shopper\Livewire\Pages\AbstractPageComponent;

class DailyReport extends AbstractPageComponent
{
    public $reportData = [];
    public $selectedDate;
    public $totalSummary = [];

    public function mount()
    {
        $this->selectedDate = now()->format('Y-m-d');
        $this->generateReport();
    }

    public function generateReport()
    {
        $startOfDay = Carbon::parse($this->selectedDate)->startOfDay();
        $endOfDay = Carbon::parse($this->selectedDate)->endOfDay();

        // Get all users who created orders today
        $salesStaff = User::whereHas('createdOrders', function (Builder $query) use ($startOfDay, $endOfDay) {
            $query->whereBetween('created_at', [$startOfDay, $endOfDay]);
        })->get();

        $this->reportData = [];
        $this->totalSummary = [
            'total_orders' => 0,
            'total_sales' => 0,
            'total_discount' => 0,
            'total_vat' => 0,
            'total_refunds' => 0,
            'total_cancelled' => 0,
        ];

        foreach ($salesStaff as $staff) {
            $staffReport = $this->generateStaffReport($staff, $startOfDay, $endOfDay);
            $this->reportData[] = $staffReport;

            // Add to totals
            $this->totalSummary['total_orders'] += $staffReport['number_of_orders'];
            $this->totalSummary['total_sales'] += $staffReport['total_sales'];
            $this->totalSummary['total_discount'] += $staffReport['total_discount'];
            $this->totalSummary['total_vat'] += $staffReport['vat_amount'];
            $this->totalSummary['total_refunds'] += $staffReport['refund_amount'];
            $this->totalSummary['total_cancelled'] += $staffReport['cancelled_orders'];
        }
    }

    public function updatedSelectedDate()
    {
        $this->generateReport();
    }

    protected function generateStaffReport(User $staff, Carbon $startOfDay, Carbon $endOfDay): array
    {
        // Get orders created by this staff today
        $orders = Order::where('user_id', $staff->id)
            ->whereBetween('created_at', [$startOfDay, $endOfDay])
            ->with(['items.product', 'taxes', 'paymentMethod'])
            ->get();

        $completedOrders = $orders->where('status', OrderStatus::Completed->value);
        $cancelledOrders = $orders->where('status', OrderStatus::Cancelled->value);

        // Calculate totals
        $totalSales = $completedOrders->sum('total_amount') ?? 0;
        $totalDiscount = $this->calculateTotalDiscount($completedOrders);
        $totalVAT = $completedOrders->sum('tax_amount') ?? 0;
        $netSales = $totalSales - $totalVAT;

        // Calculate refunds
        $refunds = OrderRefund::whereIn('order_id', $orders->pluck('id'))
            ->where('status', 'completed')
            ->sum('amount') ?? 0;

        $totalAfterRefund = $totalSales - $refunds;

        // Payment method breakdown
        $paymentMethods = $this->getPaymentMethodBreakdown($completedOrders);

        // Product sales list
        $productSales = $this->getProductSalesList($completedOrders);

        return [
            'staff_id' => $staff->id,
            'staff_name' => $staff->first_name . ' ' . $staff->last_name,
            'staff_email' => $staff->email,
            'number_of_orders' => $orders->count(),
            'completed_orders' => $completedOrders->count(),
            'total_discount' => $totalDiscount,
            'other_charges' => 0, // Placeholder for other charges
            'voucher_discount' => 0, // Placeholder for voucher discounts
            'total_sales' => $totalSales,
            'vat_amount' => $totalVAT,
            'net_sales' => $netSales,
            'refund_amount' => $refunds,
            'total_after_refund' => $totalAfterRefund,
            'cancelled_orders' => $cancelledOrders->count(),
            'payment_methods' => $paymentMethods,
            'product_sales' => $productSales,
            'orders' => $orders,
        ];
    }

    protected function calculateTotalDiscount($orders): int
    {
        $totalDiscount = 0;

        foreach ($orders as $order) {
            // Calculate discount as difference between item prices and order total
            $itemsTotal = $order->items->sum(function ($item) {
                return $item->unit_price_amount * $item->quantity;
            });

            $orderSubtotal = $order->price_amount ?? 0;
            $discount = $itemsTotal - $orderSubtotal;

            if ($discount > 0) {
                $totalDiscount += $discount;
            }
        }

        return $totalDiscount;
    }

    protected function getPaymentMethodBreakdown($orders): array
    {
        $paymentMethods = [];

        foreach ($orders as $order) {
            $methodName = $order->paymentMethod->title ?? 'Unknown';
            $amount = $order->total_amount ?? 0;

            if (!isset($paymentMethods[$methodName])) {
                $paymentMethods[$methodName] = [
                    'name' => $methodName,
                    'count' => 0,
                    'total_amount' => 0,
                ];
            }

            $paymentMethods[$methodName]['count']++;
            $paymentMethods[$methodName]['total_amount'] += $amount;
        }

        return array_values($paymentMethods);
    }

    protected function getProductSalesList($orders): array
    {
        $productSales = [];

        foreach ($orders as $order) {
            foreach ($order->items as $item) {
                $productId = $item->product_id;
                $productName = $item->name;
                $sku = $item->sku;

                if (!isset($productSales[$productId])) {
                    $productSales[$productId] = [
                        'product_id' => $productId,
                        'product_name' => $productName,
                        'sku' => $sku,
                        'quantity_sold' => 0,
                        'total_amount' => 0,
                        'unit_price' => $item->unit_price_amount,
                    ];
                }

                $productSales[$productId]['quantity_sold'] += $item->quantity;
                $productSales[$productId]['total_amount'] += $item->unit_price_amount * $item->quantity;
            }
        }

        // Sort by quantity sold (descending)
        uasort($productSales, function ($a, $b) {
            return $b['quantity_sold'] <=> $a['quantity_sold'];
        });

        return array_values($productSales);
    }



    public function render()
    {
        return view('livewire.shopper.pages.report.daily-report');
    }
}

<?php

namespace App\Livewire\Shopper\Pages\Report;

use Carbon\Carbon;
use Shopper\Core\Models\Order;
use Shopper\Livewire\Pages\AbstractPageComponent;

class SaleReport extends AbstractPageComponent
{
    public $selectedPeriod = 'today';
    public $startDate;
    public $endDate;
    public $reportData = [];
    public $totalSummary = [];

    public $periods = [
        'today' => 'Hôm nay',
        'yesterday' => 'Hôm qua',
        'last_7_days' => '7 ngày qua',
        'last_month' => 'Tháng trước',
        'last_3_months' => '3 tháng qua',
    ];

    public function mount()
    {
        $this->setDateRange();
        $this->generateReport();
    }

    public function updatedSelectedPeriod()
    {
        $this->setDateRange();
        $this->generateReport();
    }

    public function setDateRange()
    {
        $now = Carbon::now();

        switch ($this->selectedPeriod) {
            case 'today':
                $this->startDate = $now->copy()->startOfDay();
                $this->endDate = $now->copy()->endOfDay();
                break;

            case 'yesterday':
                $this->startDate = $now->copy()->subDay()->startOfDay();
                $this->endDate = $now->copy()->subDay()->endOfDay();
                break;

            case 'last_7_days':
                $this->startDate = $now->copy()->subDays(7)->startOfDay();
                $this->endDate = $now->copy()->endOfDay();
                break;

            case 'last_month':
                $this->startDate = $now->copy()->subMonth()->startOfMonth();
                $this->endDate = $now->copy()->subMonth()->endOfMonth();
                break;

            case 'last_3_months':
                $this->startDate = $now->copy()->subMonths(3)->startOfMonth();
                $this->endDate = $now->copy()->endOfMonth();
                break;

            default:
                $this->startDate = $now->copy()->startOfDay();
                $this->endDate = $now->copy()->endOfDay();
        }
    }

    public function generateReport()
    {
        $this->reportData = $this->getSalesData();
        $this->totalSummary = $this->calculateTotalSummary();
    }

    private function getSalesData()
    {
        $orders = Order::with(['items.product', 'paymentMethod'])
            ->whereBetween('created_at', [$this->startDate, $this->endDate])
            ->where('status', 'completed')
            ->get();

        $salesData = [];

        foreach ($orders as $order) {
            $date = $order->created_at->format('Y-m-d');

            if (!isset($salesData[$date])) {
                $salesData[$date] = [
                    'date' => $date,
                    'orders_count' => 0,
                    'total_sales' => 0,
                    'total_discount' => 0,
                    'total_tax' => 0,
                    'net_sales' => 0,
                    'orders' => [],
                ];
            }

            $salesData[$date]['orders_count']++;
            $salesData[$date]['total_sales'] += $order->total();
            $salesData[$date]['total_discount'] += $order->discount_amount ?? 0;
            $salesData[$date]['total_tax'] += $order->tax_amount ?? 0;
            $salesData[$date]['net_sales'] += ($order->total() - ($order->discount_amount ?? 0));

            // Add order details
            $salesData[$date]['orders'][] = [
                'id' => $order->id,
                'number' => $order->number ?? '#' . $order->id,
                'total' => $order->total(),
                'discount' => $order->discount_amount ?? 0,
                'tax' => $order->tax_amount ?? 0,
                'payment_method' => $order->paymentMethod->title ?? 'Unknown',
                'created_at' => $order->created_at,
                'items_count' => $order->items->count(),
            ];
        }

        // Sort orders by creation time (newest first) for each date
        foreach ($salesData as &$dayData) {
            usort($dayData['orders'], function ($a, $b) {
                return $b['created_at'] <=> $a['created_at'];
            });
        }

        return array_values($salesData);
    }

    private function calculateTotalSummary()
    {
        $summary = [
            'total_orders' => 0,
            'total_sales' => 0,
            'total_discount' => 0,
            'total_tax' => 0,
            'net_sales' => 0,
            'average_order_value' => 0,
        ];

        foreach ($this->reportData as $dayData) {
            $summary['total_orders'] += $dayData['orders_count'];
            $summary['total_sales'] += $dayData['total_sales'];
            $summary['total_discount'] += $dayData['total_discount'];
            $summary['total_tax'] += $dayData['total_tax'];
            $summary['net_sales'] += $dayData['net_sales'];
        }

        if ($summary['total_orders'] > 0) {
            $summary['average_order_value'] = $summary['total_sales'] / $summary['total_orders'];
        }

        return $summary;
    }

    public function getVietnameseDayName($date)
    {
        return vietnamese_day_name($date);
    }

    public function getFormattedVietnameseDate($date)
    {
        return vietnamese_formatted_date($date);
    }

    public function exportToCSV()
    {
        $filename = 'sale-report-' . $this->selectedPeriod . '-' . now()->format('Y-m-d-H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function () {
            $file = fopen('php://output', 'w');

            // CSV Headers
            fputcsv($file, [
                'Ngày',
                'Mã đơn hàng',
                'Thời gian',
                'Số sản phẩm',
                'Phương thức thanh toán',
                'Giảm giá',
                'Tổng tiền'
            ]);

            // Data rows
            foreach ($this->reportData as $dayData) {
                foreach ($dayData['orders'] as $order) {
                    fputcsv($file, [
                        $this->getFormattedVietnameseDate($dayData['date']),
                        $order['number'],
                        $order['created_at']->format('H:i:s'),
                        $order['items_count'],
                        $order['payment_method'],
                        $order['discount'] / 100,
                        $order['total'] / 100,
                    ]);
                }
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    public function printReport()
    {
        $this->dispatch('print-report');
    }

    public function render()
    {
        return view('livewire.shopper.pages.report.sale-report');
    }
}

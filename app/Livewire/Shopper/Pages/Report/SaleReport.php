<?php

namespace App\Livewire\Shopper\Pages\Report;

use Carbon\Carbon;
use Shopper\Core\Models\Order;
use Shopper\Livewire\Pages\AbstractPageComponent;

class SaleReport extends AbstractPageComponent
{
    public $selectedPeriod = 'today';
    public $startDate;
    public $endDate;
    public $reportData = [];
    public $totalSummary = [];

    public $periods = [
        'today' => 'Hôm nay',
        'yesterday' => 'Hôm qua',
        'last_7_days' => '7 ngày qua',
        'last_month' => 'Tháng trước',
        'last_3_months' => '3 tháng qua',
    ];

    public function mount()
    {
        $this->setDateRange();
        $this->generateReport();
    }

    public function updatedSelectedPeriod()
    {
        $this->setDateRange();
        $this->generateReport();
    }

    public function setDateRange()
    {
        $now = Carbon::now();

        switch ($this->selectedPeriod) {
            case 'today':
                $this->startDate = $now->copy()->startOfDay();
                $this->endDate = $now->copy()->endOfDay();
                break;

            case 'yesterday':
                $this->startDate = $now->copy()->subDay()->startOfDay();
                $this->endDate = $now->copy()->subDay()->endOfDay();
                break;

            case 'last_7_days':
                $this->startDate = $now->copy()->subDays(7)->startOfDay();
                $this->endDate = $now->copy()->endOfDay();
                break;

            case 'last_month':
                $this->startDate = $now->copy()->subMonth()->startOfMonth();
                $this->endDate = $now->copy()->subMonth()->endOfMonth();
                break;

            case 'last_3_months':
                $this->startDate = $now->copy()->subMonths(3)->startOfMonth();
                $this->endDate = $now->copy()->endOfMonth();
                break;

            default:
                $this->startDate = $now->copy()->startOfDay();
                $this->endDate = $now->copy()->endOfDay();
        }
    }

    public function generateReport()
    {
        $this->reportData = $this->getSalesData();
        $this->totalSummary = $this->calculateTotalSummary();
    }

    private function getSalesData()
    {
        $orders = Order::with(['items.product', 'paymentMethod'])
            ->whereBetween('created_at', [$this->startDate, $this->endDate])
            ->where('status', 'completed')
            ->get();

        $salesData = [];

        foreach ($orders as $order) {
            $date = $order->created_at->format('Y-m-d');

            if (!isset($salesData[$date])) {
                $salesData[$date] = [
                    'date' => $date,
                    'orders_count' => 0,
                    'total_sales' => 0,
                    'total_discount' => 0,
                    'total_tax' => 0,
                    'net_sales' => 0,
                    'payment_methods' => [],
                    'top_products' => [],
                ];
            }

            $salesData[$date]['orders_count']++;
            $salesData[$date]['total_sales'] += $order->total();
            $salesData[$date]['total_discount'] += $order->discount_amount ?? 0;
            $salesData[$date]['total_tax'] += $order->tax_amount ?? 0;
            $salesData[$date]['net_sales'] += ($order->total() - ($order->discount_amount ?? 0));

            // Payment methods
            $paymentMethod = $order->paymentMethod->title ?? 'Unknown';
            if (!isset($salesData[$date]['payment_methods'][$paymentMethod])) {
                $salesData[$date]['payment_methods'][$paymentMethod] = [
                    'name' => $paymentMethod,
                    'count' => 0,
                    'total_amount' => 0,
                ];
            }
            $salesData[$date]['payment_methods'][$paymentMethod]['count']++;
            $salesData[$date]['payment_methods'][$paymentMethod]['total_amount'] += $order->total();

            // Products
            foreach ($order->items as $item) {
                $productName = $item->product->name ?? 'Unknown Product';
                if (!isset($salesData[$date]['top_products'][$productName])) {
                    $salesData[$date]['top_products'][$productName] = [
                        'name' => $productName,
                        'quantity' => 0,
                        'total_amount' => 0,
                    ];
                }
                $salesData[$date]['top_products'][$productName]['quantity'] += $item->quantity;
                $salesData[$date]['top_products'][$productName]['total_amount'] += $item->total;
            }
        }

        // Sort products by quantity for each date
        foreach ($salesData as &$dayData) {
            uasort($dayData['top_products'], function ($a, $b) {
                return $b['quantity'] <=> $a['quantity'];
            });
            $dayData['top_products'] = array_slice($dayData['top_products'], 0, 5, true);
        }

        return array_values($salesData);
    }

    private function calculateTotalSummary()
    {
        $summary = [
            'total_orders' => 0,
            'total_sales' => 0,
            'total_discount' => 0,
            'total_tax' => 0,
            'net_sales' => 0,
            'average_order_value' => 0,
        ];

        foreach ($this->reportData as $dayData) {
            $summary['total_orders'] += $dayData['orders_count'];
            $summary['total_sales'] += $dayData['total_sales'];
            $summary['total_discount'] += $dayData['total_discount'];
            $summary['total_tax'] += $dayData['total_tax'];
            $summary['net_sales'] += $dayData['net_sales'];
        }

        if ($summary['total_orders'] > 0) {
            $summary['average_order_value'] = $summary['total_sales'] / $summary['total_orders'];
        }

        return $summary;
    }

    public function getVietnameseDayName($date)
    {
        return vietnamese_day_name($date);
    }

    public function getFormattedVietnameseDate($date)
    {
        return vietnamese_formatted_date($date);
    }

    public function exportToCSV()
    {
        $filename = 'sale-report-' . $this->selectedPeriod . '-' . now()->format('Y-m-d-H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function () {
            $file = fopen('php://output', 'w');

            // CSV Headers
            fputcsv($file, [
                'Ngày',
                'Số đơn hàng',
                'Tổng doanh thu',
                'Tổng giảm giá',
                'Tổng thuế',
                'Doanh thu ròng'
            ]);

            // Data rows
            foreach ($this->reportData as $dayData) {
                fputcsv($file, [
                    $this->getFormattedVietnameseDate($dayData['date']),
                    $dayData['orders_count'],
                    $dayData['total_sales'] / 100,
                    $dayData['total_discount'] / 100,
                    $dayData['total_tax'] / 100,
                    $dayData['net_sales'] / 100,
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    public function printReport()
    {
        $this->dispatch('print-report');
    }

    public function render()
    {
        return view('livewire.shopper.pages.report.sale-report');
    }
}

<?php

declare(strict_types=1);

namespace App\Livewire\Shopper\Pages\Tax;

use App\Models\Tax;
use App\Models\TaxRate;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Actions\CreateAction;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Grid;
use Shopper\Livewire\Pages\AbstractPageComponent;
use Shopper\Core\Models\Zone;
use Shopper\Core\Models\Currency;

class Rates extends AbstractPageComponent implements HasForms, HasTable
{
    use InteractsWithForms;
    use InteractsWithTable;

    public Tax $tax;

    public function mount(Tax $tax): void
    {
        // $this->authorize('browse_taxes');
        $this->tax = $tax;
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(
                TaxRate::query()
                    ->where('tax_id', $this->tax->id)
                    ->with(['zone', 'currency'])
                    ->latest()
            )
            ->columns([
                TextColumn::make('zone.name')
                    ->label('Zone')
                    ->default('Global')
                    ->sortable(),
                TextColumn::make('currency.code')
                    ->label('Currency')
                    ->default('All Currencies')
                    ->sortable(),
                TextColumn::make('rate')
                    ->label('Rate')
                    ->getStateUsing(function (TaxRate $record) {
                        if ($this->tax->type === 'percentage') {
                            return number_format($record->rate, 4) . '%';
                        }
                        return shopper_money_format($record->fixed_amount ?? 0);
                    })
                    ->sortable(),
                IconColumn::make('is_default')
                    ->label('Default')
                    ->boolean(),
                TextColumn::make('effective_from')
                    ->label('Effective From')
                    ->date()
                    ->default('Immediately')
                    ->sortable(),
                TextColumn::make('effective_until')
                    ->label('Effective Until')
                    ->date()
                    ->default('Indefinitely')
                    ->sortable(),
                TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                EditAction::make()
                    ->form($this->getFormSchema()),
                DeleteAction::make(),
            ])
            ->bulkActions([
                //
            ])
            ->headerActions([
                CreateAction::make()
                    ->form($this->getFormSchema())
                    ->mutateFormDataUsing(function (array $data): array {
                        $data['tax_id'] = $this->tax->id;
                        return $data;
                    }),
            ]);
    }

    protected function getFormSchema(): array
    {
        return [
            Grid::make(2)
                ->schema([
                    Select::make('zone_id')
                        ->label('Zone')
                        ->options(Zone::pluck('name', 'id'))
                        ->placeholder('Global (All Zones)')
                        ->helperText('Leave empty for global rate'),
                    Select::make('currency_id')
                        ->label('Currency')
                        ->options(Currency::pluck('name', 'id'))
                        ->placeholder('All Currencies')
                        ->native(false)
                        ->searchable()
                        ->helperText('Leave empty for all currencies'),
                ]),

            Grid::make(2)
                ->schema([
                    TextInput::make('rate')
                        ->label($this->tax->type === 'percentage' ? 'Rate (%)' : 'Rate')
                        ->numeric()
                        ->step(0.0001)
                        ->required()
                        ->visible($this->tax->type === 'percentage')
                        ->helperText('Enter percentage rate (e.g., 20.00 for 20%)'),
                    TextInput::make('fixed_amount')
                        ->label('Fixed Amount (cents)')
                        ->numeric()
                        ->required()
                        ->visible($this->tax->type === 'fixed_amount')
                        ->helperText('Enter amount in cents (e.g., 100 for $1.00)'),
                ]),

            Grid::make(2)
                ->schema([
                    DatePicker::make('effective_from')
                        ->label('Effective From')
                        ->helperText('Leave empty for immediate effect'),
                    DatePicker::make('effective_until')
                        ->label('Effective Until')
                        ->helperText('Leave empty for indefinite period'),
                ]),

            Toggle::make('is_default')
                ->label('Default Rate')
                ->helperText('Use this rate when no zone-specific rate is found')
                ->columnSpanFull(),
        ];
    }

    public function render()
    {
        return view('livewire.shopper.pages.tax.rates');
    }
}

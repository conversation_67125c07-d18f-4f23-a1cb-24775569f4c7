<?php

declare(strict_types=1);

namespace App\Livewire\Shopper\Pages\Tax;

use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\CreateAction;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Grid;
use Filament\Forms\Form;
use Shopper\Core\Models\Tax;
use Shopper\Livewire\Pages\AbstractPageComponent;

class Index extends AbstractPageComponent implements HasForms, HasTable
{
    use InteractsWithForms;
    use InteractsWithTable;

    public function mount(): void
    {
        // $this->authorize('browse_taxes');
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(Tax::query()->with('taxRates')->latest())
            ->columns([
                TextColumn::make('name')
                    ->label('Tax Name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('code')
                    ->label('Tax Code')
                    ->searchable()
                    ->sortable()
                    ->badge(),
                TextColumn::make('type')
                    ->label('Type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'percentage' => 'success',
                        'fixed_amount' => 'warning',
                        default => 'gray',
                    }),
                TextColumn::make('taxRates')
                    ->label('Default Rate')
                    ->getStateUsing(function (Tax $record) {
                        $defaultRate = $record->taxRates()->where('is_default', true)->first();
                        if (!$defaultRate) {
                            return 'No default rate';
                        }

                        if ($record->type === 'percentage') {
                            return format_tax_rate((float) $defaultRate->rate);
                        }

                        return shopper_money_format($defaultRate->fixed_amount ?? 0);
                    }),
                IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean(),
                IconColumn::make('is_inclusive')
                    ->label('Inclusive')
                    ->boolean(),
                IconColumn::make('is_compound')
                    ->label('Compound')
                    ->boolean(),
                TextColumn::make('priority')
                    ->label('Priority')
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                // EditAction::make()
                //     ->form($this->getForm()),
                Action::make('manage_rates')
                    ->label('Quản lý giá')
                    ->icon('heroicon-o-currency-dollar')
                    ->url(fn (Tax $record): string => route('shopper.settings.taxes.rates', $record)),
                DeleteAction::make(),
            ])
            ->bulkActions([
                //
            ])
            ->headerActions([
                CreateAction::make()
                    ->label('Tạo mới')
                    ->form($this->commonFormSchema()),
            ]);
    }

    public function commonFormSchema(): array
    {
        return [
            Grid::make(2)
                ->schema([
                    TextInput::make('name')
                        ->label('Tax Name')
                        ->required()
                        ->maxLength(255),
                    TextInput::make('code')
                        ->label('Tax Code')
                        ->required()
                        ->maxLength(10)
                        ->unique(Tax::class, 'code', ignoreRecord: true)
                        ->rules(['alpha_dash'])
                        ->helperText('Unique identifier for this tax (e.g., VAT, GST, ST)'),
                ]),

            Grid::make(2)
                ->schema([
                    Select::make('type')
                        ->label('Tax Type')
                        ->required()
                        ->options([
                            'percentage' => 'Percentage',
                            'fixed_amount' => 'Fixed Amount',
                        ])
                        ->default('percentage'),
                    TextInput::make('priority')
                        ->label('Priority')
                        ->numeric()
                        ->default(0)
                        ->helperText('Used for compound tax calculations (higher priority = calculated later)'),
                ]),

            Textarea::make('description')
                ->label('Description')
                ->maxLength(500)
                ->columnSpanFull(),

            Grid::make(3)
                ->schema([
                    Toggle::make('is_active')
                        ->label('Active')
                        ->default(true)
                        ->helperText('Whether this tax is currently active'),
                    Toggle::make('is_inclusive')
                        ->label('Tax Inclusive')
                        ->default(false)
                        ->helperText('Tax is included in the product price'),
                    Toggle::make('is_compound')
                        ->label('Compound Tax')
                        ->default(false)
                        ->helperText('Calculate on price + other taxes'),
                ]),
        ];
    }

    public function render()
    {
        return view('livewire.shopper.pages.tax.index');
    }
}

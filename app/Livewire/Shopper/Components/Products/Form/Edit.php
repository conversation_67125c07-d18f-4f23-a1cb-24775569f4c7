<?php

declare(strict_types=1);

namespace App\Livewire\Shopper\Components\Products\Form;

use Code<PERSON>ithDennis\FilamentSelectTree\SelectTree;
use Filament\Forms;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Support\Enums\IconSize;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;
use Livewire\Component;
use Shopper\Actions\Store\Product\UpdateProductAction;
use Shopper\Components;
use Shopper\Core\Models\Product;
use Shopper\Core\Models\Tax;
use Shopper\Feature;

/**
 * @property Forms\Form $form
 */
class Edit extends Component implements HasForms
{
    use InteractsWithForms;

    /**
     * @var Product
     */
    public $product;

    /**
     * @var array<array-key, mixed>|null
     */
    public ?array $data = [];

    public function mount($product): void
    {
        $this->product = $product;

        // Load product data including taxes
        $productData = $this->product->toArray();

        // Load existing taxes
        $productData['taxes'] = $this->product->taxes()->pluck(shopper_table('taxes') . '.id')->toArray();

        // Load custom tax rates
        $customRates = [];
        $customFixedAmounts = [];

        foreach ($this->product->taxes as $tax) {
            if ($tax->pivot->custom_rate) {
                $customRates[$tax->id] = $tax->pivot->custom_rate;
            }
            if ($tax->pivot->custom_fixed_amount) {
                $customFixedAmounts[$tax->id] = $tax->pivot->custom_fixed_amount;
            }
        }

        $productData['custom_rates'] = $customRates;
        $productData['custom_fixed_amounts'] = $customFixedAmounts;

        $this->form->fill($productData);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Grid::make()
                    ->schema([
                        Forms\Components\Section::make(__('shopper::pages/products.general'))
                            ->description($this->product->type?->getDescription())
                            ->icon($this->product->type?->getIcon())
                            ->iconSize(IconSize::Large)
                            ->schema([
                                Forms\Components\TextInput::make('name')
                                    ->label(__('shopper::forms.label.name'))
                                    ->required()
                                    ->maxLength(255)
                                    ->live(onBlur: true)
                                    ->afterStateUpdated(function ($state, Forms\Set $set): void {
                                        $set('slug', Str::slug($state));
                                    }),
                                Forms\Components\TextInput::make('slug')
                                    ->label(__('shopper::forms.label.slug'))
                                    ->disabled()
                                    ->dehydrated()
                                    ->required()
                                    ->maxLength(255)
                                    ->unique(config('shopper.models.product'), 'slug', ignoreRecord: true),
                                Forms\Components\Textarea::make('summary')
                                    ->label(__('shopper::forms.label.summary'))
                                    ->columnSpan('full'),
                                Forms\Components\RichEditor::make('description')
                                    ->label(__('shopper::forms.label.description'))
                                    ->columnSpan('full'),

                                Forms\Components\Group::make()
                                    ->schema([
                                        Components\Separator::make()
                                            ->columnSpanFull(),
                                        Forms\Components\TextInput::make('external_id')
                                            ->label(__('shopper::forms.label.external_id'))
                                            ->unique(config('shopper.models.product'), 'external_id', ignoreRecord: true)
                                            ->helperText(__('shopper::pages/products.external_id_description')),
                                    ])
                                    ->columnSpanFull()
                                    ->columns()
                                    ->visible($this->product->isExternal()),
                            ])
                            ->columns(),
                    ])
                    ->columnSpan(['lg' => 2]),

                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make(__('shopper::pages/products.status'))
                            ->schema([
                                Forms\Components\Toggle::make('is_visible')
                                    ->label(__('shopper::forms.label.visible'))
                                    ->helperText(__('shopper::pages/products.visible_help_text'))
                                    ->onColor('success')
                                    ->default(true),

                                Forms\Components\DateTimePicker::make('published_at')
                                    ->label(__('shopper::forms.label.availability'))
                                    ->native(false)
                                    ->helperText(__('shopper::pages/products.availability_description'))
                                    ->required(),
                            ]),

                        // Tax Configuration Section
                        Forms\Components\Section::make('Cài đặt thuế')
                            ->schema($this->getTaxFormSchema()),

                        Forms\Components\Section::make(__('shopper::pages/products.product_associations'))
                            ->schema([
                                Forms\Components\Select::make('brand_id')
                                    ->label(__('shopper::forms.label.brand'))
                                    ->relationship('brand', 'name', fn (Builder $query) => $query->where('is_enabled', true))
                                    ->searchable()
                                    ->visible(Feature::enabled('brand')),

                                SelectTree::make('categories')
                                    ->label(__('shopper::pages/categories.menu'))
                                    ->enableBranchNode()
                                    ->relationship(
                                        relationship: 'categories',
                                        titleAttribute: 'name',
                                        parentAttribute: 'parent_id',
                                        modifyQueryUsing: fn (Builder $query) => $query->where('is_enabled', true)
                                    )
                                    ->searchable()
                                    ->visible(Feature::enabled('category'))
                                    ->withCount(),

                                Forms\Components\Select::make('channels')
                                    ->label(__('shopper::pages/settings/menu.sales'))
                                    ->relationship(
                                        name: 'channels',
                                        titleAttribute: 'name',
                                        modifyQueryUsing: fn (Builder $query) => $query->where('is_enabled', true)
                                    )
                                    ->searchable()
                                    ->preload()
                                    ->multiple(),

                                Forms\Components\Select::make('collections')
                                    ->label(__('shopper::pages/collections.menu'))
                                    ->relationship('collections', 'name')
                                    ->searchable()
                                    ->preload()
                                    ->multiple()
                                    ->visible(Feature::enabled('collection')),
                            ])
                            ->visible(
                                Feature::enabled('brand')
                                    || Feature::enabled('category')
                                    || Feature::enabled('collection')
                            ),
                    ])
                    ->columnSpan(['lg' => 1]),
            ])
            ->columns(3)
            ->statePath('data')
            ->model($this->product);
    }

    protected function getTaxFormSchema(): array
    {
        $activeTaxes = Tax::active()->get();

        if ($activeTaxes->isEmpty()) {
            return [
                Forms\Components\Placeholder::make('no_taxes')
                    ->label('No taxes available')
                    ->content('No active taxes found. Please create taxes first.')
            ];
        }

        $schema = [
            Forms\Components\CheckboxList::make('taxes')
                ->hiddenLabel()
                ->options(function () use ($activeTaxes) {
                    $data = [];
                    foreach ($activeTaxes as $tax) {
                        $defaultRate = $tax->taxRates()->where('is_default', true)->first();
                        if (!$defaultRate) {
                            $data[$tax->id] = "{$tax->name} (Chưa cấu hình)";
                        } elseif ($tax->type === 'percentage') {
                            $rate = format_tax_rate((float) $defaultRate->rate);
                            $data[$tax->id] = "{$tax->name} ($rate)";
                        } else {
                            $data[$tax->id] = "{$tax->name} (Chưa cấu hình)";
                        }
                    }
                    return $data;
                })
                ->descriptions($activeTaxes->pluck('description', 'id')),
        ];

        return $schema;
    }

    public function store(): void
    {
        $this->validate();

        $data = $this->form->getState();

        // Handle tax relationships
        $this->syncProductTaxes($data);

        $this->product = app()->call(UpdateProductAction::class, [
            'form' => $this->form,
            'product' => $this->product,
        ]);

        $this->dispatch('product.updated');

        Notification::make()
            ->title(__('shopper::notifications.update', ['item' => __('shopper::pages/products.single')]))
            ->success()
            ->send();
    }

    protected function syncProductTaxes(array $data): void
    {
        $selectedTaxes = $data['taxes'] ?? [];

        // Prepare sync data
        $syncData = [];

        foreach ($selectedTaxes as $taxId) {
            $syncData[$taxId] = [
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        // Sync taxes with the product
        $this->product->taxes()->sync($syncData);
    }

    public function render(): View
    {
        return view('shopper::livewire.components.products.forms.edit');
    }
}

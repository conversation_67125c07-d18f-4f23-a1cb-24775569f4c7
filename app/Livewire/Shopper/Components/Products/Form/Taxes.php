<?php

declare(strict_types=1);

namespace App\Livewire\Shopper\Components\Products\Form;

use App\Models\Tax;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Livewire\Component;
use Shopper\Core\Models\Product;

class Taxes extends Component implements HasForms
{
    use InteractsWithForms;

    public Product $product;
    public array $selectedTaxes = [];
    public array $customRates = [];
    public array $customFixedAmounts = [];

    public function mount(Product $product): void
    {
        $this->product = $product;
        $this->loadProductTaxes();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Tax Configuration')
                    ->description('Configure which taxes apply to this product and set custom rates if needed.')
                    ->schema([
                        CheckboxList::make('selectedTaxes')
                            ->label('Applied Taxes')
                            ->options(Tax::active()->pluck('name', 'id'))
                            ->descriptions(Tax::active()->pluck('description', 'id'))
                            ->live()
                            ->afterStateUpdated(fn () => $this->updateCustomRateFields()),
                        
                        Grid::make(2)
                            ->schema($this->getCustomRateFields())
                            ->visible(fn () => !empty($this->selectedTaxes)),
                    ]),
            ])
            ->statePath('data');
    }

    protected function getCustomRateFields(): array
    {
        $fields = [];
        
        foreach ($this->selectedTaxes as $taxId) {
            $tax = Tax::find($taxId);
            if (!$tax) continue;

            if ($tax->type === 'percentage') {
                $fields[] = TextInput::make("customRates.{$taxId}")
                    ->label("Custom Rate for {$tax->name} (%)")
                    ->numeric()
                    ->step(0.0001)
                    ->placeholder('Leave empty to use default rate')
                    ->helperText("Default rate will be used if empty");
            } else {
                $fields[] = TextInput::make("customFixedAmounts.{$taxId}")
                    ->label("Custom Amount for {$tax->name} (cents)")
                    ->numeric()
                    ->placeholder('Leave empty to use default amount')
                    ->helperText("Default amount will be used if empty");
            }
        }

        return $fields;
    }

    public function loadProductTaxes(): void
    {
        $productTaxes = $this->product->taxes()->get();
        
        $this->selectedTaxes = $productTaxes->pluck('id')->toArray();
        
        foreach ($productTaxes as $tax) {
            if ($tax->pivot->custom_rate) {
                $this->customRates[$tax->id] = $tax->pivot->custom_rate;
            }
            if ($tax->pivot->custom_fixed_amount) {
                $this->customFixedAmounts[$tax->id] = $tax->pivot->custom_fixed_amount;
            }
        }
    }

    public function updateCustomRateFields(): void
    {
        // Remove custom rates for unselected taxes
        $this->customRates = array_intersect_key($this->customRates, array_flip($this->selectedTaxes));
        $this->customFixedAmounts = array_intersect_key($this->customFixedAmounts, array_flip($this->selectedTaxes));
    }

    public function save(): void
    {
        $this->validate();

        // Prepare sync data
        $syncData = [];
        
        foreach ($this->selectedTaxes as $taxId) {
            $syncData[$taxId] = [
                'is_active' => true,
                'custom_rate' => $this->customRates[$taxId] ?? null,
                'custom_fixed_amount' => $this->customFixedAmounts[$taxId] ?? null,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        // Sync taxes with the product
        $this->product->taxes()->sync($syncData);

        $this->dispatch('notify', [
            'type' => 'success',
            'message' => 'Product taxes updated successfully!',
        ]);
    }

    public function render()
    {
        return view('livewire.shopper.components.products.form.taxes');
    }
}

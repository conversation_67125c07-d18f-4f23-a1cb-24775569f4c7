<?php

namespace App\Livewire\Shopper\Components\Modals;

use Filament\Forms\Components\DatePicker;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Shopper\Livewire\Components\ModalComponent;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Grid;
use Filament\Notifications\Notification;
use Shopper\Core\Models\Currency;
use Shopper\Core\Models\Tax;
use Shopper\Core\Models\TaxRate;
use Shopper\Core\Models\Zone;

class CreateTaxRate extends ModalComponent implements HasForms
{
    use InteractsWithForms;
    public ?array $data = [];
    public $tax;

    public function mount($tax): void
    {
        $this->tax = $tax;
        $this->form->fill([]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make(2)
                    ->schema([
                        Select::make('zone_id')
                            ->label('Vùng')
                            ->options(Zone::pluck('name', 'id'))
                            ->placeholder('Global (All Zones)')
                            ->helperText('Leave empty for global rate'),
                        Select::make('currency_id')
                            ->label('Tiền tệ')
                            ->options(Currency::pluck('name', 'id'))
                            ->placeholder('Tất cả các loại tiền tệ')
                            ->native(false)
                            ->searchable()
                            ->helperText('Để trống cho tất cả các loại tiền tệ'),
                    ]),

                Grid::make(2)
                    ->schema([
                        TextInput::make('rate')
                            ->label($this->tax['type'] === 'percentage' ? 'Rate (%)' : 'Rate')
                            ->numeric()
                            ->step(0.0001)
                            ->required()
                            ->visible($this->tax['type'] === 'percentage')
                            ->helperText('Nhập tỷ lệ phần trăm (ví dụ: 20.00 cho 20%)'),
                        TextInput::make('fixed_amount')
                            ->label('Số tiền cố định (cents)')
                            ->numeric()
                            ->required()
                            ->visible($this->tax['type'] === 'fixed_amount')
                            ->helperText('Nhập số tiền cố định (ví dụ: 100 cho $1.00)'),
                    ]),

                Grid::make(2)
                    ->schema([
                        DatePicker::make('effective_from')
                            ->label('Có hiệu lực từ')
                            ->helperText('Để trống để có hiệu lực ngay lập tức'),
                        DatePicker::make('effective_until')
                            ->label('Có hiệu lực đến')
                            ->helperText('Để trống để có hiệu lực mãi mãi'),
                    ]),

                Toggle::make('is_default')
                    ->label('Mức thuế mặc định')
                    ->helperText('Sử dụng mức thuế này khi không tìm thấy mức thuế theo vùng')
                    ->columnSpanFull(),
            ])
            ->statePath('data');
    }

    public function save(): void
    {
        $data = $this->form->getState();
        $data['tax_id'] = $this->tax['id'];
        $taxRate = TaxRate::query()->create($data);

        Notification::make()
            ->title(__('Mức thuế mới đã được tạo thành công'))
            ->success()
            ->send();

        $this->dispatch('taxRateAdded');

        $this->closeModal();
    }

    public static function modalMaxWidth(): string
    {
        return '4xl';
    }

    public function render()
    {
        return view('livewire.shopper.components.modals.create-tax-rate');
    }
}

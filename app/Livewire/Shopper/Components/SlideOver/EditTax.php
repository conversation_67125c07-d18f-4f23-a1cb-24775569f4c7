<?php

namespace App\Livewire\Shopper\Components\SlideOver;

use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Livewire\Component;
use Shopper\Core\Models\Tax;
use Shopper\Livewire\Components\SlideOverComponent;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Grid;
use Filament\Notifications\Notification;

class EditTax extends SlideOverComponent implements HasForms
{
    use InteractsWithForms;
    public $tax;

    public ?array $data = [];

    public function mount(?int $taxId = null): void
    {
        $this->tax = Tax::query()->find($taxId);
        $this->form->fill($this->tax->toArray());
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make(2)
                    ->schema([
                        TextInput::make('name')
                            ->label('Tên thuế')
                            ->required()
                            ->maxLength(255),
                        TextInput::make('code')
                            ->label('Mã thuế')
                            ->required()
                            ->maxLength(10)
                            ->unique(Tax::class, 'code', ignoreRecord: true)
                            ->rules(['alpha_dash'])
                            ->helperText('Mã định danh duy nhất cho loại thuế này (ví dụ: VAT, GST, ST)'),
                    ]),

                Grid::make(2)
                    ->schema([
                        Select::make('type')
                            ->label('Loại thuế')
                            ->required()
                            ->options([
                                'percentage' => 'Percentage',
                                'fixed_amount' => 'Fixed Amount',
                            ])
                            ->default('percentage'),
                        TextInput::make('priority')
                            ->label('Độ ưu tiên')
                            ->numeric()
                            ->default(0)
                            ->helperText('Sử dụng cho các tính toán thuế tổng hợp (ưu tiên cao hơn = tính toán sau)'),
                    ]),

                Textarea::make('description')
                    ->label('Description')
                    ->maxLength(500)
                    ->columnSpanFull(),

                Grid::make(3)
                    ->schema([
                        Toggle::make('is_active')
                            ->label('Hoạt động')
                            ->default(true)
                            ->helperText('Thuế này hiện đang có hiệu lực không'),
                        Toggle::make('is_inclusive')
                            ->label('Bao gồm thuế')
                            ->default(false)
                            ->helperText('Thuế được bao gồm trong giá sản phẩm không'),
                        Toggle::make('is_compound')
                            ->label('Thuế tổng hợp')
                            ->default(false)
                            ->helperText('Tính toán trên giá sản phẩm + các loại thuế khác'),
                    ]),
            ])
            ->statePath('data')
            ->model($this->tax);
    }

    public function save(): void
    {
        $this->tax->update($this->form->getState());

        Notification::make()
            ->title(__('Chỉnh sửa thuế thành công'))
            ->success()
            ->send();

        $this->dispatch('taxAdded');

        $this->closePanel();
    }

    public function render()
    {
        return view('livewire.shopper.components.slide-over.edit-tax');
    }
}

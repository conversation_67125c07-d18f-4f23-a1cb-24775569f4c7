<?php

namespace App\Livewire\Shopper\Components\Setting\Pages;

use App\Jobs\SyncInvoiceVATJob;
use App\Models\EInvoiceConfig;
use Livewire\Attributes\Layout;
use Shopper\Livewire\Pages\AbstractPageComponent;
use Illuminate\Support\Facades\Http;
use Exception;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Notifications\Notification;
use Shopper\Core\Enum\OrderStatus;
use Shopper\Core\Models\Order;

#[Layout('shopper::components.layouts.setting')]
class EInvoiceIndex extends AbstractPageComponent implements HasForms
{
    use InteractsWithForms;
    public ?array $data;

    public $providerCode;
    public $invoiceIntegrationConfig = [
        'link_api' => '',
        'username' => '',
        'username_pass' => '',
        'account' => '',
        'account_pass' => '',
        'pattern_cash_register' => '',
        'serial_cash_register' => '',
    ];

    public $loginStatus = null;
    public $loginMessage = '';
    public $syncInvoiceInfo = [
        'date' => 'today',
    ];
    public $isSelect = true;

    public function checkLogin()
    {
        $this->loginStatus = null;
        $this->loginMessage = '';

        try {
            $xmlRequest = '<?xml version="1.0" encoding="utf-8"?>
            <soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
              <soap:Body>
                <loginportal xmlns="http://tempuri.org/">
                  <Account>' . $this->invoiceIntegrationConfig['account'] . '</Account>
                  <ACpass>' . $this->invoiceIntegrationConfig['account_pass'] . '</ACpass>
                  <userName>' . $this->invoiceIntegrationConfig['username'] . '</userName>
                  <pass>' . $this->invoiceIntegrationConfig['username_pass'] . '</pass>
                  <comid></comid>
                </loginportal>
              </soap:Body>
            </soap:Envelope>';

            $response = Http::withHeaders([
                'Content-Type' => 'text/xml; charset=utf-8',
                'SOAPAction' => 'http://tempuri.org/loginportal',
                'Content-Length' => strlen($xmlRequest),
            ])->withBody($xmlRequest, 'text/xml')
                ->post($this->invoiceIntegrationConfig['link_api'] . '/PortalService.asmx');

            if ($response->successful()) {
                $responseXml = simplexml_load_string($response->body());
                $responseXml->registerXPathNamespace('soap', 'http://schemas.xmlsoap.org/soap/envelope/');
                $responseXml->registerXPathNamespace('ns', 'http://tempuri.org/');

                $result = $responseXml->xpath('//soap:Body/ns:loginportalResponse/ns:loginportalResult');
                dd($result);

                if (!empty($result)) {
                    $resultValue = (string)$result[0];

                    if (strpos($resultValue, 'ERR:') === 0) {
                        $this->loginStatus = false;
                        $this->loginMessage = 'Đăng nhập thất bại: ' . substr($resultValue, 4);
                    } else {
                        $this->loginStatus = true;
                        $this->loginMessage = 'Đăng nhập thành công!';
                    }
                } else {
                    $this->loginStatus = false;
                    $this->loginMessage = 'Không thể xác thực thông tin đăng nhập.';
                }
            } else {
                $this->loginStatus = false;
                $this->loginMessage = 'Lỗi kết nối: ' . $response->status();
            }
        } catch (Exception $e) {
            dd($e);
            $this->loginStatus = false;
            $this->loginMessage = 'Lỗi: ' . $e->getMessage();
        }
    }

    public function save()
    {
        $data = $this->validate([
            'invoiceIntegrationConfig.link_api' => 'required',
            'invoiceIntegrationConfig.username' => 'required',
            'invoiceIntegrationConfig.username_pass' => 'required',
            'invoiceIntegrationConfig.account' => 'required',
            'invoiceIntegrationConfig.account_pass' => 'required',
            'invoiceIntegrationConfig.pattern_cash_register' => 'nullable',
            'invoiceIntegrationConfig.serial_cash_register' => 'nullable',
        ]);

        EInvoiceConfig::updateOrCreate(
            [
                'provider_code' => $this->providerCode,
            ],
            [
                'config' => $data['invoiceIntegrationConfig'],
                'is_enabled' => true,
            ]
        );

        Notification::make()
            ->title('Cấu hình hoá đơn điện tử đã được lưu')
            ->success()
            ->send();
    }

    public function mount()
    {
        $activeProvider = EInvoiceConfig::where('is_enabled', true)->first();
        if ($activeProvider) {
            $this->providerCode = $activeProvider->provider_code;
            $this->invoiceIntegrationConfig = $activeProvider->config;
        }
        $this->form->fill([
            'date' => 'today',
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('date')
                    ->required()
                    ->markAsRequired()
                    ->label('Thời gian bán hàng')
                    ->options([
                        'today' => 'Hôm nay',
                        'custom' => 'Tuỳ chỉnh',
                    ])
                    ->default('today')
                    ->live()
                    ->columnSpanFull(),
                DateTimePicker::make('from_date')
                    ->label('Từ ngày')
                    ->locale('vi')
                    ->seconds(false)
                    ->columnSpan(1)
                    ->visible(fn (Get $get) => $get('date') == 'custom')
                    ->required(fn (Get $get) => $get('date') == 'custom')
                    ->markAsRequired(),
                DateTimePicker::make('to_date')
                    ->label('Đến ngày')
                    ->locale('vi')
                    ->seconds(false)
                    ->columnSpan(1)
                    ->visible(fn (Get $get) => $get('date') == 'custom')
                    ->required(fn (Get $get) => $get('date') == 'custom')
                    ->markAsRequired(),
                Checkbox::make('is_only_export_invoice')
                    ->label('Chỉ đồng bộ các đơn hàng yêu cầu xuất HĐĐT từ máy tính tiền')
                    ->columnSpanFull(),
            ])
            ->columns(2)
            ->statePath('data');
    }

    public function syncInvoice()
    {
        $validated = $this->form->getState();


        if ($validated['date'] == 'today') {
            $orders = Order::whereDate('created_at', today())
                ->when(isset($validated['is_only_export_invoice']), function ($query) {
                    $query->where('is_export_invoice', true);
                })
                ->whereNull('e_invoice_authority_issued_code')
                ->where('status', OrderStatus::Completed)
                ->get();
            if (count($orders)) {
                foreach ($orders as $order) {
                    dispatch(new SyncInvoiceVATJob($order, auth()->user()));
                }
                Notification::make()
                    ->title('Đang đồng bộ các hoá đơn điện tử của ngày hôm nay')
                    ->success()
                    ->send();
            } else {
                Notification::make()
                    ->title('Không có hoá đơn nào để đồng bộ')
                    ->warning()
                    ->send();
            }
        }
        if ($validated['date'] == 'custom') {
            dd($validated);
            Notification::make()
                ->title('Chức năng đang phát triển!')
                ->warning()
                ->send();
        }
    }

    public function render()
    {
        return view('livewire.shopper.components.setting.pages.e-invoice-index');
    }
}

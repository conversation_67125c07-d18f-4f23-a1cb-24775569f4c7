<?php

namespace App\Livewire\Shopper\Components\Setting\Pages;

use App\Models\EInvoiceConfig;
use Livewire\Attributes\Layout;
use Shopper\Livewire\Pages\AbstractPageComponent;
use Illuminate\Support\Facades\Http;
use Exception;
use Filament\Notifications\Notification;

#[Layout('shopper::components.layouts.setting')]
class EInvoiceIndex extends AbstractPageComponent
{
    public $providerCode;
    public $invoiceIntegrationConfig = [
        'link_api' => '',
        'username' => '',
        'username_pass' => '',
        'account' => '',
        'account_pass' => '',
        'pattern_cash_register' => '',
        'serial_cash_register' => '',
    ];

    public $loginStatus = null;
    public $loginMessage = '';
    public $syncInvoiceInfo = [
        'date' => 'today',
    ];
    public $isSelect = true;

    public function checkLogin()
    {
        $this->loginStatus = null;
        $this->loginMessage = '';

        try {
            $xmlRequest = '<?xml version="1.0" encoding="utf-8"?>
            <soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
              <soap:Body>
                <loginportal xmlns="http://tempuri.org/">
                  <Account>' . $this->invoiceIntegrationConfig['account'] . '</Account>
                  <ACpass>' . $this->invoiceIntegrationConfig['account_pass'] . '</ACpass>
                  <userName>' . $this->invoiceIntegrationConfig['username'] . '</userName>
                  <pass>' . $this->invoiceIntegrationConfig['username_pass'] . '</pass>
                  <comid></comid>
                </loginportal>
              </soap:Body>
            </soap:Envelope>';

            $response = Http::withHeaders([
                'Content-Type' => 'text/xml; charset=utf-8',
                'SOAPAction' => 'http://tempuri.org/loginportal',
                'Content-Length' => strlen($xmlRequest),
            ])->withBody($xmlRequest, 'text/xml')
                ->post($this->invoiceIntegrationConfig['link_api'] . '/PortalService.asmx');

            if ($response->successful()) {
                $responseXml = simplexml_load_string($response->body());
                $responseXml->registerXPathNamespace('soap', 'http://schemas.xmlsoap.org/soap/envelope/');
                $responseXml->registerXPathNamespace('ns', 'http://tempuri.org/');

                $result = $responseXml->xpath('//soap:Body/ns:loginportalResponse/ns:loginportalResult');
                dd($result);

                if (!empty($result)) {
                    $resultValue = (string)$result[0];

                    if (strpos($resultValue, 'ERR:') === 0) {
                        $this->loginStatus = false;
                        $this->loginMessage = 'Đăng nhập thất bại: ' . substr($resultValue, 4);
                    } else {
                        $this->loginStatus = true;
                        $this->loginMessage = 'Đăng nhập thành công!';
                    }
                } else {
                    $this->loginStatus = false;
                    $this->loginMessage = 'Không thể xác thực thông tin đăng nhập.';
                }
            } else {
                $this->loginStatus = false;
                $this->loginMessage = 'Lỗi kết nối: ' . $response->status();
            }
        } catch (Exception $e) {
            dd($e);
            $this->loginStatus = false;
            $this->loginMessage = 'Lỗi: ' . $e->getMessage();
        }
    }

    public function save()
    {
        $data = $this->validate([
            'invoiceIntegrationConfig.link_api' => 'required',
            'invoiceIntegrationConfig.username' => 'required',
            'invoiceIntegrationConfig.username_pass' => 'required',
            'invoiceIntegrationConfig.account' => 'required',
            'invoiceIntegrationConfig.account_pass' => 'required',
            'invoiceIntegrationConfig.pattern_cash_register' => 'nullable',
            'invoiceIntegrationConfig.serial_cash_register' => 'nullable',
        ]);

        EInvoiceConfig::updateOrCreate(
            [
                'provider_code' => $this->providerCode,
            ],
            [
                'config' => $data['invoiceIntegrationConfig'],
                'is_enabled' => true,
            ]
        );

        Notification::make()
            ->title('Cấu hình hoá đơn điện tử đã được lưu')
            ->success()
            ->send();
    }

    public function mount()
    {
        $activeProvider = EInvoiceConfig::where('is_enabled', true)->first();
        if ($activeProvider) {
            $this->providerCode = $activeProvider->provider_code;
            $this->invoiceIntegrationConfig = $activeProvider->config;
        }
    }

    public function render()
    {
        return view('livewire.shopper.components.setting.pages.e-invoice-index');
    }
}

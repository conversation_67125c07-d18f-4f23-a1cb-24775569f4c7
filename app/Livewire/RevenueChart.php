<?php

namespace App\Livewire;

use Carbon\Carbon;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;
use Shopper\Core\Enum\OrderStatus;
use Shopper\Core\Models\Order;

class RevenueChart extends ChartWidget
{
    protected static ?string $heading = 'Doanh thu 7 ngày gần đây';

    protected static ?string $maxHeight = '300px';



    protected function getData(): array
    {
        $data = $this->getRevenueData();

        return [
            'datasets' => [
                [
                    'label' => 'Doanh thu',
                    'data' => $data['revenues'],
                ],
            ],
            'labels' => $data['dates'],
        ];
    }

    protected function getRevenueData(): array
    {
        $dates = collect();
        $revenues = collect();

        // Get last 7 days
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $dates->push($date->format('d/m'));

            // Get completed orders for this day
            $dailyRevenue = Order::where('status', OrderStatus::Completed)
                ->whereDate('created_at', $date->format('Y-m-d'))
                ->get()
                ->sum(function ($order) {
                    return $order->total_amount;
                });

            $revenues->push($dailyRevenue);
        }

        return [
            'dates' => $dates->toArray(),
            'revenues' => $revenues->toArray(),
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }
}

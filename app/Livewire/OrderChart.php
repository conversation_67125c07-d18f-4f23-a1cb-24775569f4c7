<?php

namespace App\Livewire;

use Carbon\Carbon;
use Filament\Widgets\ChartWidget;
use Shopper\Core\Models\Order;

class OrderChart extends ChartWidget
{
    protected static ?string $heading = 'Đơn hàng 7 ngày gần đây';
    protected static ?string $maxHeight = '300px';
    protected static string $color = 'danger';





    protected function getData(): array
    {
        $data = $this->getOrderData();

        return [
            'datasets' => [
                [
                    'label' => 'Số lượng đơn hàng',
                    'data' => $data['counts'],
                ],
            ],
            'labels' => $data['dates'],
        ];
    }

    protected function getOrderData(): array
    {
        $dates = collect();
        $counts = collect();

        // Get last 7 days
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $dates->push($date->format('d/m'));

            // Get order count for this day
            $dailyCount = Order::whereDate('created_at', $date->format('Y-m-d'))
                ->count();

            $counts->push($dailyCount);
        }

        return [
            'dates' => $dates->toArray(),
            'counts' => $counts->toArray(),
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }
}

<?php

namespace App\Providers;

use Illuminate\Support\Facades\Route;
use Illuminate\Support\ServiceProvider;
use Livewire\Livewire;
use Stancl\Tenancy\Middleware\InitializeTenancyByDomain;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
        Livewire::setUpdateRoute(function ($handle) {
            return Route::post('/livewire/update', $handle)
                ->middleware(
                    'web',
                    'universal',
                    InitializeTenancyByDomain::class, // or whatever tenancy middleware you use
                );
        });

        \Illuminate\Support\Facades\URL::macro('tenantFile', function (?string $file = '') {
            if (is_null($file)) {
                return null;
            }

            // file is the url information, which is returned directly. No tenant domain name splicing.
            if (str_contains($file, '://')) {
                return $file;
            }

            $prefix = str_replace(
                '%tenant_id%',
                tenant()->get<PERSON><PERSON>(),
                config('tenancy.filesystem.url_override.public', 'public-%tenant_id%')
            );

            return url($prefix . '/' . $file);
        });
    }
}

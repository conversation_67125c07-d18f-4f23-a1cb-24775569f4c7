<?php

namespace App\Filament\Imports;

use App\Models\Product;
use Filament\Actions\Imports\ImportColumn;
use Filament\Actions\Imports\Importer;
use Filament\Actions\Imports\Models\Import;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Shopper\Core\Models\Category;

class ProductImporter extends Importer
{
    protected static ?string $model = Product::class;

    public static function getColumns(): array
    {
        return [
            ImportColumn::make('name')
                ->label('Tên sản phẩm')
                ->rules(['required', 'max:255']),
            ImportColumn::make('amount')
                ->label('Giá tiền')
                ->rules(['required', 'numeric', 'min:0']),
            ImportColumn::make('sku')
                ->label('Mã SKU')
                ->rules(['nullable', 'max:255']),
            ImportColumn::make('barcode')
                ->label('Mã vạch')
                ->rules(['nullable', 'max:255']),
            ImportColumn::make('description')
                ->label('Mô tả')
                ->rules(['nullable']),
            ImportColumn::make('categories')
                ->label('Danh mục (phân cách bằng dấu phẩy)')
                ->rules(['nullable']),
        ];
    }

    public function resolveRecord(): ?Product
    {
        return Product::firstOrNew([
            'sku' => $this->data['sku'],
        ]);

        return new Product();
    }

    protected function beforeCreate(): void
    {
    }

    protected function beforeFill(): void
    {
        unset($this->data['amount']);
        unset($this->data['categories']);
    }

    public function saveRecord(): void
    {
        $this->record->type = 'standard';
        $this->record->slug = $this->generateUniqueSlug($this->record->name);
        $this->record->save();
    }

    protected function afterSave(): void
    {
        if (isset($this->originalData['amount'])) {
            $this->record->prices()->delete();
            $this->record->prices()->create([
                'amount' => $this->originalData['amount'],
                'compare_amount' => 0,
                'cost_amount' => 0,
                'currency_id' => 148,
            ]);
        }
        if (isset($this->originalData['categories'])) {
            if (!empty($this->originalData['categories'])) {
                $categories = explode(',', $this->originalData['categories']);
                foreach ($categories as $key => $category) {
                    $findCategory = Category::where('name', $category)->first();
                    if ($findCategory) {
                        $this->record->categories()->sync([$findCategory->id]);
                    }
                }
            }
        }
    }

    /**
     * Generate unique slug from product name
     *
     * @param string $name The product name to generate slug from
     * @return string Unique slug with incremental suffix if needed (e.g., product-name-01, product-name-02)
     */
    protected function generateUniqueSlug(string $name): string
    {
        // Clean and generate base slug from name
        $baseSlug = Str::slug(trim($name));

        // If base slug is empty (e.g., name contains only special characters), use a default
        if (empty($baseSlug)) {
            $baseSlug = 'product-' . time(); // Add timestamp to make it unique
        }

        // Limit slug length to avoid database issues
        $baseSlug = Str::limit($baseSlug, 200, '');

        $slug = $baseSlug;
        $counter = 1;

        // Check if slug exists and increment until we find a unique one
        while (Product::where('slug', $slug)->exists()) {
            // Format: product-name-01, product-name-02, etc.
            $suffix = '-' . str_pad($counter, 2, '0', STR_PAD_LEFT);

            // Ensure the slug with suffix doesn't exceed length limits
            $maxBaseLength = 200 - strlen($suffix);
            $truncatedBase = Str::limit($baseSlug, $maxBaseLength, '');

            $slug = $truncatedBase . $suffix;
            $counter++;

            // Safety check to prevent infinite loop (though very unlikely)
            if ($counter > 999) {
                $slug = $baseSlug . '-' . time();
                break;
            }
        }

        return $slug;
    }

    public static function getCompletedNotificationBody(Import $import): string
    {
        $body = 'Your product import has completed and ' . number_format($import->successful_rows) . ' ' . str('row')->plural($import->successful_rows) . ' imported.';

        if ($failedRowsCount = $import->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('row')->plural($failedRowsCount) . ' failed to import.';
        }

        return $body;
    }
}

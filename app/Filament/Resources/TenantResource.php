<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TenantResource\Pages;
use App\Filament\Resources\TenantResource\RelationManagers;
use App\Models\Domain;
use App\Models\Tenant;
use Filament\Forms;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Validation\Rules\Password;
use Illuminate\Validation\Rules\Unique;
use Closure;

class TenantResource extends Resource
{
    protected static ?string $model = Tenant::class;
    protected static ?string $label = 'Cửa hàng';

    protected static ?string $navigationIcon = 'heroicon-o-building-storefront';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Section::make()
                            ->schema([
                                Forms\Components\TextInput::make('name')
                                    ->label('Tên cửa hàng')
                                    ->required()
                                    ->maxLength(255),
                                Forms\Components\TextInput::make('domain')
                                    ->label('Tên miền')
                                    ->required()
                                    ->prefix('https://')
                                    ->suffix("." . config('tenancy.primary_central_domain'))
                                    ->rules([
                                        fn (Get $get): Closure => function (string $attribute, $value, Closure $fail) use ($get) {
                                            $domain = strtolower(trim($value)) . "." . config('tenancy.primary_central_domain');
                                            if (Domain::where('domain', $domain)->exists()) {
                                                $fail("Tên miền đã tồn tại.");
                                            }
                                        },
                                    ]),

                            ]),
                        Forms\Components\Section::make()
                            ->schema([
                                Forms\Components\TextInput::make('email')
                                    ->label('Email tài khoản quản trị')
                                    ->email()
                                    ->required()
                                    ->maxLength(255),
                                Forms\Components\TextInput::make('password')
                                    ->label('Mật khẩu tài khoản quản trị')
                                    ->password()
                                    ->required()
                                    ->rule(Password::default())
                                    ->revealable()
                                    ->maxLength(255),

                            ]),
                    ])->columnSpan(2),
                Forms\Components\Section::make()
                    ->schema([
                        Toggle::make('is_visible')
                            ->label('Hoạt động?')
                            ->default(true),
                    ])->columnSpan(1),


            ])->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->searchable(),
                Tables\Columns\TextColumn::make('name')
                    ->label('Tên cửa hàng')
                    ->searchable(),
                Tables\Columns\TextColumn::make('email')
                    ->searchable(),
                Tables\Columns\TextColumn::make('domains.domain')
                    ->label('Tên miền')
                    ->prefix('https://')
                    ->url(fn ($record) => 'https://' . $record->domains->first()->domain, true)
                    ->searchable(),
                ToggleColumn::make('is_visible')
                    ->label('Hoạt động?'),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTenants::route('/'),
            'create' => Pages\CreateTenant::route('/create'),
            'edit' => Pages\EditTenant::route('/{record}/edit'),
        ];
    }
}

<?php

namespace App\Jobs;

use App\Services\VNPTService;
use Filament\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Shopper\Core\Models\Order;

class SyncInvoiceVATJob implements ShouldQueue
{
    use Queueable;

    protected Order $order;
    protected $recipient;

    /**
     * Create a new job instance.
     */
    public function __construct(Order $order, $recipient)
    {
        $this->order = $order;
        $this->recipient = $recipient;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $VNPTService = new VNPTService($this->order);
        $result = $VNPTService->createInvoiceVat();
        if ($result['status']) {
            $this->order->update([
                'e_invoice_provider_code' => $result['data']['provider_code'],
                'e_invoice_pattern' => $result['data']['pattern'],
                'e_invoice_serial' => $result['data']['serial'],
                'e_invoice_invoice_number' => $result['data']['invoice_number'],
                'e_invoice_authority_issued_code' => $result['data']['authority_issued_code'],
            ]);
            Notification::make()
                ->title('Đã đồng bộ hoá đơn điện tử thành công cho đơn hàng #' . $this->order->number)
                ->success()
                ->sendToDatabase($this->recipient);
        }
    }
}

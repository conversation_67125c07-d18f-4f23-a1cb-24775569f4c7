<?php

namespace App\Jobs;

use App\Models\Tenant;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\File;
use Shopper\Core\Enum\AddressType;
use Shopper\Core\Models\Permission;
use Shopper\Core\Models\Role;

class CreateTenantUser implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(protected Tenant $tenant)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {

        $this->tenant->run(function () {
            Artisan::call('tenants:seed', [
                '--tenants' => [$this->tenant->id],
                '--class' => 'ShopperSeeder',
                '--force' => true,
            ]);
            $user = User::create([
                'last_name' => $this->tenant->name,
                'email' => $this->tenant->email,
                'password' => $this->tenant->password,
                'email_verified_at' => now()
            ]);
            Permission::where('guard_name', 'admin-web')->update([
                'guard_name' => 'web',
            ]);
            Role::where('guard_name', 'admin-web')->update([
                'guard_name' => 'web',
            ]);
            $user->assignRole(config('shopper.core.users.admin_role'));


            $customer = User::create([
                'first_name' => 'Khách',
                'last_name' => 'vãng lai',
                'email' => '<EMAIL>',
            ]);
            $customer->assignRole(config('shopper.core.users.default_role'));
            $address = [
                'first_name' => 'Khách',
                'last_name' => 'vãng lai',
                'type' => AddressType::Shipping,
                'street_address' => 'Chưa xác định',
                'postal_code' => '000000',
                'city' => 'Chưa xác định',
                'phone_number' => 'Chưa xác định',
                'country_id' => 243,
                'user_id' => $customer->id,
            ];
            $customer->addresses()->create($address);
        });

        // $basePath = storage_path("tenant{$this->tenant->id}/framework/cache");

        // if (!File::exists($basePath)) {
        //     File::makeDirectory($basePath, 0755, true);
        // }
    }
}

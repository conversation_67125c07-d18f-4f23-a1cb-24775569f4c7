<?php

declare(strict_types=1);

namespace App\Actions;

use App\Services\TaxService;
use Darryldecode\Cart\Facades\CartFacade;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Shopper\Core\Enum\OrderStatus;
use Shopper\Core\Models\Order;
use Shopper\Core\Models\OrderItem;

final class CreateOrder
{
    protected $taxService;

    public function __construct()
    {
        $this->taxService = new TaxService();
    }

    public function handle($data): Order
    {
        $sessionId = Session::getId();
        // $customer = Auth::user();

        $order = Order::query()->create([
            'number' => generate_number(),
            'status' => $data['status'],
            'customer_id' => $data['customerId'] ?? 2,
            'currency_code' => current_currency(),
            'payment_method_id' => $data['payment_method_id'],
            'shipping_address_id' => $data['shipping_address_id'] ?? null,
            'zone_id' => $data['zone_id'] ?? null,
            'is_export_invoice' => $data['is_export_invoice'] ?? false,
        ]);

        $totalOrderAmount = 0;

        // @phpstan-ignore-next-line
        foreach (CartFacade::session($sessionId)->getContent() as $item) {
            $orderItem = OrderItem::query()->create([
                'order_id' => $order->id,
                'quantity' => $item->quantity,
                'unit_price_amount' => $item->price,
                'name' => $item->name,
                'sku' => $item->associatedModel->sku,
                'product_id' => $item->id,
                'product_type' => $item->associatedModel->getMorphClass(),
            ]);

            // Calculate and save taxes for this order item
            $itemTaxes = $this->taxService->calculateOrderItemTaxes(
                $orderItem,
                $order->zone_id,
                $order->getCurrencyId()
            );
            Log::debug($itemTaxes);

            if (!empty($itemTaxes['tax_breakdown'])) {
                $this->taxService->saveOrderItemTaxes($orderItem, $itemTaxes);
            }

            $totalOrderAmount += ($item->price * $item->quantity) + $itemTaxes['total_tax_amount'];
        }

        // Calculate and save order-level taxes
        $orderTaxes = $this->taxService->calculateOrderTaxes($order);
        if ($orderTaxes['total_tax_amount'] > 0) {
            $this->taxService->saveOrderTaxes($order, $orderTaxes);
        }

        // Calculate totals
        $subtotal = $totalOrderAmount - $orderTaxes['total_tax_amount'];

        // Update order with calculated amounts
        $order->update([
            'price_amount' => $subtotal,
            'tax_amount' => $orderTaxes['total_tax_amount'],
            'total_amount' => $totalOrderAmount,
        ]);

        return $order;
    }
}

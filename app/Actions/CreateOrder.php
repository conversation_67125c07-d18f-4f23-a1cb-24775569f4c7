<?php

declare(strict_types=1);

namespace App\Actions;

use Darrylde<PERSON>\Cart\Facades\CartFacade;
use Illuminate\Support\Facades\Session;
use Shopper\Core\Enum\OrderStatus;
use Shopper\Core\Models\Order;
use Shopper\Core\Models\OrderItem;

final class CreateOrder
{
    public function handle($data): Order
    {
        $sessionId = Session::getId();
        // $customer = Auth::user();


        $order = Order::query()->create([
            'number' => generate_number(),
            'status' => $data['status'],
            'customer_id' => $data['customerId'] ?? 2,
            'currency_code' => current_currency(),
            'payment_method_id' => $data['payment_method_id'],
            'shipping_address_id' => $data['shipping_address_id'] ?? null,
            'is_export_invoice' => $data['is_export_invoice'] ?? false,
        ]);

        // @phpstan-ignore-next-line
        foreach (CartFacade::session($sessionId)->getContent() as $item) {
            OrderItem::query()->create([
                'order_id' => $order->id,
                'quantity' => $item->quantity,
                'unit_price_amount' => $item->price,
                'name' => $item->name,
                'sku' => $item->associatedModel->sku,
                'product_id' => $item->id,
                'product_type' => $item->associatedModel->getMorphClass(),
            ]);
        }

        return $order;
    }
}

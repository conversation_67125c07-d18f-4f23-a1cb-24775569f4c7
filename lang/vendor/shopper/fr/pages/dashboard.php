<?php

declare(strict_types=1);

return [

    'menu' => 'Tableau de bord',
    'welcome_message' => 'Bienvenue sur le tableau de bord de Shopper',
    'header' => 'Commencez par les éléments de base de votre boutique en ligne',
    'description' => 'Pour commencer à construire votre nouvelle boutique avec Laravel Shopper, nous vous recommandons de commencer par ces étapes.
                            Le framework vous permet de créer votre boutique et de la configurer exactement comme vous le souhaitez.
                            Vous pouvez faire des intégrations pour aller plus vite si vous le souhaitez.',
    'cards' => [
        'doc_title' => 'Documentation',
        'doc_description' => 'Apprenez à connaître Laravel Shopper en comprenant ses capacités de la bonne manière, que vous soyez nouveau sur le framework ou que vous ayez déjà travaillé dessus. Cette documentation est faite pour vous.',
        'doc_link' => 'Visitez la documentation',

        'screencast_title' => 'Screencasts',
        'screencast_description' => 'Apprenez à construire une boutique en ligne professionnelle du début à la fin avec des leçons vidéo complètes sur Shopper et des exemples de codes pour configurer rapidement votre boutique.',
        'screencast_link' => 'Commencez à regarder',

        'theme_title' => 'Thèmes',
        'theme_description' => 'Votre boutique est le site Web de vos produits. Soyez rapidement opérationnel grâce à un thème disponible, spécialement créé pour Shopper. Modifiez-le selon vos besoins ou créez votre propre thème.',
        'theme_link' => 'Chercher un Thème',

        'product_title' => 'Ajout produit',
        'product_description' => 'Ajoutez des produits et des prix pour commencer à vendre. Adaptez-le aux besoins de votre magasin avec un nombre illimité de produits (selon la taille de votre magasin), de marques, de collections et de variations.',
        'product_link' => 'Ajouter un produit',
    ],

];

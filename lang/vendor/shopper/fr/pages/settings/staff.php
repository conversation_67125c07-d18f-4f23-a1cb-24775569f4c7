<?php

declare(strict_types=1);

return [

    'title' => 'Rôles des utilisateurs et gestion des accès',
    'header_title' => 'Administrateurs et rôles',
    'role_available' => 'Rôles d\'administrateur disponible',
    'role_available_summary' => "Un rôle donne accès à des menus et à des fonctions prédéfinis, de sorte qu'en fonction du rôle et des autorisations qui lui sont attribués, un administrateur peut avoir accès à ce dont il a besoin.",
    'new_role' => 'Ajouter un rôle',
    'admin_accounts' => 'Comptes administrateurs',
    'admin_accounts_summary' => "Il s'agit des membres qui sont déjà présents dans votre magasin avec les rôles qui leur sont associés. Vous pouvez attribuer de nouveaux rôles aux membres existants ici.",
    'add_admin' => 'Ajouter un administrateur',
    'users_role' => 'Utilisateurs et rôles',
    'login_information' => 'Informations de connexion',
    'login_information_summary' => "Ces informations seront utiles à l'administrateur pour se connecter à l'administration de Shopper.",
    'send_invite' => 'Envoyer l\'invitation',
    'send_invite_summary' => 'Envoyez une invitation à cet administrateur par courrier électronique avec ses informations de connexion.',
    'personal_information' => 'Information personnelle',
    'personal_information_summary' => 'Informations relatives au profil de l\'administrateur.',
    'role_information' => 'Informations sur le rôle',
    'role_information_summary' => 'Attribuez à cet administrateur des rôles qui limiteront les actions qu\'il peut effectuer.',
    'roles' => 'Rôles',
    'role' => 'Rôle',
    'permission' => 'Permission',
    'permissions' => 'Permissions',
    'choose_role' => 'Choisissez un rôle pour cet administrateur',
    'create_permission' => 'Créer une permission',
    'role_alert_msg' => "Vous êtes sur le point de mettre à jour le rôle d'administrateur, ce qui pourrait bloquer votre accès au tableau de bord.",
    'with_role_name' => 'avec le rôle :name',
    'permissions_in_role' => 'pour le rôle :name',
    'custom_permission' => 'Permission personnalisée',
    'delete_team_member' => 'Êtes-vous sûr de vouloir supprimer ce membre ?',

];

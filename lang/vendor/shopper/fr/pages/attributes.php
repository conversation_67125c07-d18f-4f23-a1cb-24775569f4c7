<?php

declare(strict_types=1);

return [

    'menu' => 'Attributs',
    'single' => 'attribut',
    'title' => 'Gérer les attributs',
    'content' => 'Ajoutez des attributs personnalisés à votre produit pour afficher des informations',
    'add' => 'Ajouter un attribut',
    'update' => 'Modifier l\'attribut :attribute',
    'searchable_description' => 'Vous pouvez utiliser cet attribut pour rechercher et filtrer des produits.',
    'filtrable_description' => 'Vous pouvez utiliser cet attribut comme filtre dans votre boutique en ligne.',
    'attribute_visibility' => 'Définir la visibilité des attributs pour les clients.',
    'attribute_value' => 'Valeur de l\'attribut id',
    'description' => 'Les attributes associés à votre produit. Ces attributs une fois sélectionnés, pourront être associés pour générer une combinaison de variantes',

    'values' => [
        'slug' => 'Valeurs',
        'title' => 'Valeurs d\'attributs',
        'description' => 'Ajouter des valeurs par défaut pour cet attribut. Ces valeurs pourront être disponibles dans l\' onglet attributs des produits.',
    ],

    'notifications' => [
        'save' => 'L\'attribut a été enrégistré avec succès!',
        'value_created' => 'Nouvelle valeur ajoutée pour :name',
        'value_updated' => 'Votre valeur a été correctement mise à jour',
        'value_removed' => 'Votre valeur a été correctement supprimée',
    ],

];

<?php

declare(strict_types=1);

return [

    'menu' => 'Đơn hàng',
    'single' => 'đơn hàng',
    'title' => 'Quản lý đơn hàng khách hàng',
    'show_title' => 'Chi tiết đơn hàng ~ :number',
    'content' => '<PERSON><PERSON> khách hàng đặt hàng, đ<PERSON>y là nơi xử lý tất cả các đơn hàng, quản lý hoàn tiền và theo dõi đơn hàng của họ.',
    'total_price_description' => 'Giá này chưa bao gồm các loại thuế áp dụng cho sản phẩm hoặc khách hàng.',

    'no_shipping_method' => 'Đơn hàng này chưa có phương thức vận chuyển',
    'read_about_shipping' => 'Tìm hiểu thêm về vận chuyển',
    'no_payment_method' => 'Đ<PERSON>n hàng này chưa có phương thức thanh toán',
    'read_about_payment' => 'Tìm hiểu thêm về phương thức thanh toán',
    'payment_actions' => 'Thao tác thanh toán',
    'send_invoice' => 'Gửi hóa đơn',
    'private_notes' => 'Ghi chú riêng',
    'customer_date' => 'Khách hàng từ :date',
    'customer_orders' => 'đã đặt :number đơn hàng',
    'customer_infos' => 'Thông tin liên hệ',
    'customer_infos_empty' => 'Không có thông tin nào về khách hàng này',
    'no_customer' => 'Khách hàng không xác định',

    'modals' => [
        'archived_number' => 'Đơn hàng :number sẽ được lưu trữ',
        'archived_notice' => 'Bạn có chắc chắn muốn lưu trữ đơn hàng này không? Hành động này sẽ ảnh hưởng đến tổng thu nhập của bạn trong cửa hàng.',
    ],

    'notifications' => [
        'archived' => 'Đơn hàng đã được lưu trữ thành công!',
        'cancelled' => 'Đơn hàng đã được hủy thành công!',
        'note_added' => 'Ghi chú của bạn đã được thêm vào đơn hàng này.',
        'registered' => 'Đơn hàng đã được ghi nhận thành công!',
        'paid' => 'Đơn hàng đã được đánh dấu là đã thanh toán!',
        'completed' => 'Đơn hàng đã được đánh dấu là đã hoàn tất!',
    ],

];

<?php

declare(strict_types=1);

return [

    'menu' => 'Thuộc tính',
    'single' => 'thuộc tính',
    'title' => 'Quản lý Thuộc tính',
    'content' => 'Thêm các thuộc tính tùy chỉnh vào sản phẩm của bạn để hiển thị thông tin.',
    'add' => 'Thêm thuộc tính',
    'update' => 'Cập nhật thuộc tính :attribute',
    'searchable_description' => 'Bạn có thể sử dụng thuộc tính này để tìm kiếm và lọc sản phẩm.',
    'filtrable_description' => 'Bạn có thể sử dụng thuộc tính này làm bộ lọc trên cửa hàng.',
    'attribute_visibility' => 'Thiết lập hiển thị thuộc tính cho khách hàng.',
    'attribute_value' => 'ID giá trị thuộc tính',
    'description' => '<PERSON><PERSON><PERSON> thuộc tính được liên kết với sản phẩm của bạn. Sau khi chọn, các thuộc tính này có thể được kết hợp để tạo ra các biến thể.',

    'values' => [
        'slug' => 'Giá trị',
        'title' => 'Giá trị thuộc tính',
        'description' => 'Thêm các giá trị mặc định cho thuộc tính này. Những giá trị này sẽ có sẵn trong tab thuộc tính sản phẩm.',
    ],

    'notifications' => [
        'save' => 'Thuộc tính đã được lưu thành công',
        'value_created' => 'Giá trị mới đã được thêm cho :name',
        'value_updated' => 'Giá trị của bạn đã được cập nhật thành công',
        'value_removed' => 'Giá trị của bạn đã được xóa thành công',
    ],

];

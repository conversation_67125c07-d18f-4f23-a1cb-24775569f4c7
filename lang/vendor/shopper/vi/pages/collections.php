<?php

declare(strict_types=1);

return [

    'menu' => 'Bộ sưu tập',
    'single' => 'bộ sưu tập',
    'title' => 'Tổ chức sản phẩm thành các bộ sưu tập khác nhau',
    'content' => 'Tạo và quản lý tất cả bộ sưu tập để giúp khách hàng dễ dàng tìm thấy nhóm hoặc loại sản phẩm.',
    'automatic' => 'Tự động',
    'automatic_description' => 'Sản phẩm thỏa điều kiện bạn đặt sẽ tự động được thêm vào bộ sưu tập.',
    'manual' => 'Thủ công',
    'manual_description' => 'Thêm sản phẩm vào bộ sưu tập này từng cái một.',
    'filter_type' => 'Loại bộ sưu tập',
    'product_conditions' => 'Điều kiện sản phẩm',
    'availability_description' => 'Chỉ định ngày phát hành để lên lịch hiển thị bộ sưu tập trong cửa hàng.',
    'empty_collections' => 'Không có sản phẩm nào trong bộ sưu tập này. Hãy thêm hoặc thay đổi điều kiện để tự động thêm sản phẩm.',
    'remove_product' => 'Sản phẩm đã được xoá khỏi bộ sưu tập thành công.',

    'conditions' => [
        'title' => 'Điều kiện',
        'products_match' => 'Sản phẩm phải phù hợp với',
        'all' => 'Tất cả điều kiện',
        'any' => 'Bất kỳ điều kiện nào',
        'rules' => 'Quy tắc',
        'choose_rule' => 'Chọn một quy tắc',
        'select_operator' => 'Chọn toán tử',
        'add' => 'Thêm điều kiện',
        'add_another' => 'Thêm điều kiện khác',
        'update' => 'Cập nhật điều kiện thành công',
    ],

    'modal' => [
        'title' => 'Thêm sản phẩm vào bộ sưu tập',
        'search' => 'Tìm kiếm sản phẩm',
        'search_placeholder' => 'Tìm sản phẩm theo tên',
        'action' => 'Thêm sản phẩm đã chọn',
        'stock' => ':stock còn hàng',
        'success_message' => 'Sản phẩm đã chọn đã được thêm thành công',
    ],
];

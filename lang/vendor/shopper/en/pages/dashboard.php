<?php

declare(strict_types=1);

return [

    'menu' => 'Dashboard',
    'welcome_message' => 'Welcome to Shopper Dashboard',
    'header' => 'Start with the basic for your online store',
    'description' => 'To begin building your new store with Laravel Shopper, we recommend starting with these steps. The framework allows you to create
                           your store and configure it exactly as you want. You can make integrations to go faster if you want.',
    'cards' => [
        'doc_title' => 'Documentation',
        'doc_description' => 'Get to know Laravel Shopper by understanding its capabilities the right way, whether you are new to the framework or have already worked on it. This documentation is made for you.',
        'doc_link' => 'Visit the documentation',

        'screencast_title' => 'Screencasts',
        'screencast_description' => 'Learn how to Learn to build a professional online store from start to finish with complete Shopper video lessons and sample codes to quickly set up your store.',
        'screencast_link' => 'Start watching Shopper',

        'theme_title' => 'Starter Kit',
        'theme_description' => 'Your store is the website for your products. Get up and running quickly with an available starter kit, specially build for Shopper. Edit as needed or create your own theme.',
        'theme_link' => 'Find a Theme',

        'product_title' => 'Add product',
        'product_description' => 'Add products and prices to start selling. Tailor it to your store\'s needs with an unlimited number of products (depending on the size of your store), brands, collections, and variations.',
        'product_link' => 'Add product to your store',
    ],

];

<?php

declare(strict_types=1);

return [

    'title' => 'User Roles & Access Management',
    'header_title' => 'Administrators & roles',
    'role_available' => 'Administrator role available',
    'role_available_summary' => 'A role provides access to predefined menus and features so that depending on the assigned role and permissions an administrator can have access to what he needs.',
    'new_role' => 'Add new role',
    'admin_accounts' => 'Administrators accounts',
    'admin_accounts_summary' => 'These are the members who are already in your store with their associated roles. You can assign new roles to existing member here.',
    'add_admin' => 'Add administrator',
    'users_role' => 'Users & roles',
    'login_information' => 'Login information',
    'login_information_summary' => 'This information will be useful for the administrator to connect to the administration of <PERSON><PERSON>.',
    'send_invite' => 'Send Invite',
    'send_invite_summary' => 'Send an invitation to this administrator by email with his login information.',
    'personal_information' => 'Personal Information',
    'personal_information_summary' => 'Information related to the admin profile.',
    'role_information' => 'Role Information',
    'role_information_summary' => 'Assign roles to this administrator who will limit the actions he can do.',
    'roles' => 'Roles',
    'role' => 'Role',
    'permission' => 'Permission',
    'permissions' => 'Permissions',
    'choose_role' => 'Choose a role for this admin',
    'create_permission' => 'Create permission',
    'role_alert_msg' => 'You are about to update the admin role, this could block your access to the dashboard.',
    'with_role_name' => 'with :name role',
    'permissions_in_role' => 'in :name role',
    'custom_permission' => 'Custom permission',
    'delete_team_member' => 'Are you sure you want to delete this member?',

];

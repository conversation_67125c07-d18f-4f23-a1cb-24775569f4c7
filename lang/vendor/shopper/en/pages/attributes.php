<?php

declare(strict_types=1);

return [

    'menu' => 'Attributes',
    'single' => 'attribute',
    'title' => 'Manage Attributes',
    'content' => 'Add custom attributes to your product to display for informations',
    'add' => 'Add attribute',
    'update' => 'Update attribute :attribute',
    'searchable_description' => 'You can use this attribute to search and filter product.',
    'filtrable_description' => 'You can use this attribute as a filter on your front store.',
    'attribute_visibility' => 'Set attribute visibility for the customers.',
    'attribute_value' => 'Attribute value id',
    'description' => 'The attributes associated with your product. Once selected, these attributes can be combined to generate a combination of variants.',

    'values' => [
        'slug' => 'Values',
        'title' => 'Attribute values',
        'description' => 'Add default values for this attribute. These values will be available on product attributes tabs.',
    ],

    'notifications' => [
        'save' => 'Attribute has been successfully save',
        'value_created' => 'New value added for :name',
        'value_updated' => 'Your value have been correctly updated',
        'value_removed' => 'Your value have been correctly removed',
    ],

];

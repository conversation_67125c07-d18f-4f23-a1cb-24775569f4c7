<?php

declare(strict_types=1);

return [

    'account' => 'Account',
    'users' => 'Users',
    'user' => 'User',
    'system' => 'System',
    'purchased' => 'Purchased',
    'total' => 'Total',
    'subtotal' => 'Subtotal',
    'date' => 'Date',
    'per_page' => 'Per page',
    'per_page_items' => 'Per page items',
    'not_available' => 'Not available',
    'available' => 'Available',
    'stock' => 'Stock',
    'customer' => 'Customer',

    'payment_method' => 'Payment Method',
    'shipping_method' => 'Shipping method',
    'no_shipping' => 'No shipping method',
    'estimated' => 'Estimated Delivery',
    'available_methods' => 'View available methods',
    'shipping' => 'Shipping',
    'provider' => 'Provider',

    'view' => 'View',
    'no_phone_number' => 'No phone number',
    'same_address' => 'Same as shipping address',

    'showing' => 'Showing',
    'to' => 'to',
    'of' => 'of',
    'results' => 'results',
    'media' => 'Media',
    'hide' => 'Hide',
    'show' => 'Show',

    'pricing' => 'Pricing',
    'price' => 'Price',
    'dismiss' => 'Dismiss',

    'published_on' => 'Will be published on:',

    'variants' => 'Variants',
    'variant' => 'Variant',
    'overview' => 'Overview',
    'in_stock' => 'in stock',
    'out_stock' => 'out stock',
    'event' => 'Event',
    'adjustment' => 'Adjustment',
    'is_enabled' => 'Enabled',
    'is_disabled' => 'Disabled',

    'set_visibility' => 'Set :name visibility for the customers.',
    'set_global_visibility' => 'Setup page visibility for the customers.',
    'log_out' => 'Logout',
    'browser_platform' => ':browser on :platform',

    'generate' => 'Generate',
    'male' => 'Male',
    'female' => 'Female',
    'not_defined' => 'Not defined',
    'unlimited' => 'unlimited',
    'used' => 'used',
    'once_per_user' => 'Once per user',
    'scheduled' => 'Scheduled',
    'active_for_users' => 'Active For users',
    'from_date' => 'From :date',
    'summary' => 'Summary',
    'everyone' => 'For everyone',
    'for_name' => 'For :name',

    'count' => [
        'products' => ':count products',
        'customers' => 'For :count customers',
    ],

    'files' => 'Files',
    'images' => 'Images',
    'step' => 'Step :number',
    'environment' => 'Environnement',
    'discount_use' => '{1} Limit of 1 use|[2,*] Limit of :count uses',

    'no_group' => 'No group',
    'no_values' => 'No values',
    'no_users' => 'No users',
    'logout_session' => 'Logout Other Browser Sessions',
    'logout_session_confirm' => 'Please enter your password to confirm you would like to logout of your other browser sessions across all of your devices.',
    'reorder' => 'Reorder',
    'all' => 'All',
    'actions' => 'Actions',

    'socials' => [
        'facebook' => 'Facebook',
        'instagram' => 'Instagram',
        'twitter' => 'Twitter',
    ],

    'details' => 'View details',
    'view_details' => 'View details',
    'registered_on' => 'Registered on',
    'full' => 'Full',
    'limited' => 'Limited',
    'me' => 'Me',
    'invitation' => 'Invitation',
    'attention_needed' => 'Attention needed',
    'attention_description' => 'The higher role (:role) gives this user the same rights and permissions as you.',
    'wip' => 'work in progress',
    'visit_documentation' => 'visit the documentation.',
    'namespace' => 'Namespace',
    'sponsor' => [
        'description' => "Do you like this feature? It's inspired by Laravel Mail Eclipse. You can sponsor the author",
        'repo' => 'View the repo',
        'action' => 'Sponsor',
    ],
    'empty_space' => "We didn't find anything - just empty space.",
    'git' => "If your project uses git don't forget to add the created files and commit them.",
    'html' => 'HTML',
    'default' => 'Default',
    'number_not_set' => 'Number not set',
    'added_on' => 'Added on',
    'dark_mode' => 'Dark Mode',
    'view_shortcuts' => 'View shortcuts',
    'global_search' => 'Global search',
    'display_shortcuts' => 'Display shortcuts',
    'go_to_documentation' => 'Go to documentation',
    'search' => 'Search content',
    'icon_no_result' => 'No matching icons found',
    'selection' => 'Selection',
    'sign_in_as' => 'Signed in as',
    'general' => 'General',
    'configuration' => 'Configuration',
    'conditions' => 'Conditions',
    'slug_description' => 'Use to generate the right url format for your store',

    'seo' => [
        'title' => 'Search Engine Optimization',
        'slug' => 'SEO',
        'description' => 'Improve your ranking and how your :name page will appear in search engines results.',
        'sub_description' => 'Here is a preview of what an search engine can display, play with it!',
        'characters' => '160 characters', // to replace in blade file
    ],

    'other' => ' other(s)',
    'feature' => 'Feature',
    'feature_enabled' => 'You must enable :feature to activate this section.',
    'amount' => 'Amount',
    'soon' => 'Soon',
    'learn_more' => 'Learn more about',
    'characters' => ':number characters',
    'number_more' => ':number more',

];

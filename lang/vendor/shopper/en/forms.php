<?php

declare(strict_types=1);

return [

    'label' => [
        'email' => 'Email address',
        'password' => 'Password',
        'new_password' => 'New Password',
        'confirm_password' => 'Confirm Password',
        'current_password' => 'Current Password',
        'remember' => 'Remember me',
        'change' => 'Change',
        'optional' => 'Optional',
        'summary' => 'Summary',
        'description' => 'Description',
        'title' => 'Title',
        'confirm' => 'Confirm',
        'first_name' => 'First name',
        'last_name' => 'Last Name',
        'full_name' => 'Full Name',
        'phone_number' => 'Phone number',
        'name' => 'Name',
        'external_id' => 'Product external Id',
        'slug' => 'Slug',
        'website' => 'Website',
        'url' => 'Url',
        'created_at' => 'Created At',
        'updated_at' => 'Updated At',
        'published_at' => 'Published At',
        'registered_at' => 'Registered At',
        'visibility' => 'Visibility',
        'position' => 'Position',
        'image_preview' => 'Image preview',
        'any' => 'Any',
        'no' => 'No',
        'yes' => 'Yes',
        'type' => 'Type',
        'value' => 'Value',
        'browse' => 'Browse',
        'sort_by' => 'Sort by',
        'subscribed' => 'Subscribed',
        'not_subscribed' => 'Not Subscribed',
        'email_subscription' => 'Email Subscription',
        'email_verified' => 'E-mail Verified',
        'company_name' => 'Company Name',
        'street_address' => 'Street Address',
        'street_address_plus' => 'Apartment, suite, etc.',
        'country' => 'Country',
        'countries' => 'Countries',
        'city' => 'City',
        'postal_code' => 'Postal / Zip code',
        'photo' => 'Picture',
        'birth_date' => 'Birth Date',
        'gender' => 'Gender',
        'status' => 'Status',
        'active' => 'Active',
        'inactive' => 'Inactive',
        'code' => 'Code',
        'recovery_code' => 'Recovery Code',
        'visible' => 'Visible',
        'invisible' => 'Invisible',
        'search' => 'Search',
        'price' => 'Price',
        'price_amount' => 'Price amount',
        'compare_price' => 'Compare at price',
        'cost_per_item' => 'Cost per item',
        'sku' => 'SKU (Stock Keeping Unit)',
        'barcode' => 'Barcode (ISBN, UPC, GTIN, etc.)',
        'safety_stock' => 'Safety Stock',
        'width_unit' => 'Width Unit',
        'height_unit' => 'Height Unit',
        'weight_unit' => 'Weight Unit',
        'volume_unit' => 'Volume Unit',
        'depth_unit' => 'Depth Unit',
        'height' => 'Height',
        'width' => 'Width',
        'volume' => 'Volume',
        'depth' => 'Depth',
        'weight' => 'Weight',
        'availability' => 'Availability',
        'brand' => 'Brand',
        'friendly_url' => 'Friendly URL',
        'quantity' => 'Quantity',
        'stock_number_value' => 'Stock number value',
        'store_name' => 'Store name',
        'legal_name' => 'Legal name of the company',
        'logo' => 'Logo',
        'cover_photo' => 'Cover photo',
        'about' => 'About',
        'default_currency' => 'Default currency',
        'currency' => 'Currency',
        'currencies' => 'Currencies',
        'longitude' => 'Longitude',
        'latitude' => 'Latitude',
        'public_key' => 'Public key',
        'secret_key' => 'Secret key',
        'tax' => 'Tax',
        'comment' => 'Comment',
        'parent' => 'Parent',
        'provider_logo' => 'Provider logo',
        'payment_method' => 'Provider',
        'payment_doc' => 'Provider Documentation (for dev)',
        'payment_instruction' => 'Provider instructions',
        'additional_details' => 'Additional details',
        'group_name' => 'Group name',
        'display_name' => 'Display name',
        'key' => 'Key',
        'is_searchable' => 'Is Searchable',
        'is_filterable' => 'Is Filterable',
        'role' => 'Role',
        'access' => 'Access',
        'content' => 'Content',
        'force' => 'Force',
        'template' => 'Template',
        'ga_tracking_id' => 'Google Analytics Tracking ID',
        'ga_view_id' => 'Google Analytics view ID',
        'ga_additional_script' => 'Google Analytics additional script',
        'ga_json' => 'Json Account Credentials',
        'gtag' => 'Your Google Tag Manager account ID',
        'pixel_id' => 'Your Facebook Pixel account ID',
        'icon' => 'Icon',
        'thumbnail' => 'Thumbnail',
        'attribute' => 'Attribute',
        'attribute_custom_value' => 'Custom value (for text input field)',
    ],

    'placeholder' => [
        'password' => 'Enter your password',
        'email' => 'Enter your email',
        'value' => 'your value here',
        'date' => 'Choose date',
        'pick_a_date' => 'Pick a date',
        'select' => 'Select',
        'select_inventory' => 'Select inventory',
        'choose_currency' => 'Choose currency',
        'select_currencies' => 'Choose currencies',
        'select_country' => 'Choose a Country',
        'select_countries' => 'Choose countries',
        'search_by' => 'Search by :label',
        'leave_comment' => 'Leave notes for this customer',
        'search_payment' => 'Search payment by provider name',
        'icon_placeholder' => 'Search for an icon',
        'no_icon' => 'No icon selected',
    ],

    'actions' => [
        'activate' => 'Activate',
        'activated' => 'Activated',
        'confirm' => 'Confirm',
        'cancel' => 'Cancel',
        'clear' => 'Clear',
        'close' => 'Close',
        'delete' => 'Delete',
        'disable' => 'Disable',
        'edit' => 'Edit',
        'view' => 'View',
        'enable' => 'Enable',
        'export' => 'Export',
        'nevermind' => 'Nevermind',
        'update' => 'Update',
        'save' => 'Save',
        'subscribe' => 'Subscribe',
        'remove' => 'Remove',
        'remove_all' => 'Remove All',
        'enabled_two_factor' => 'Enable Two Factor',
        'regenerate_code' => 'Regenerate Recovery Codes',
        'show_recovery_code' => 'Show Recovery Codes',
        'archive' => 'Archive',
        'more_actions' => 'More actions',
        'mark_paid' => 'Mark as paid',
        'mark_complete' => 'Mark complete',
        'cancel_order' => 'Cancel Order',
        'send' => 'Send',
        'logout_session' => 'Logout other browser sessions',
        'approve' => 'Approve',
        'disapprove' => 'Disapprove',
        'create' => 'Create',
        'upload' => 'Upload',
        'verified' => 'Verified',
        'apply' => 'Apply',
        'next' => 'Next',
        'back' => 'Back',
        'theme_switcher' => 'Enable :label theme',

        'add_label' => 'New :label',
        'edit_label' => 'Edit :label',
        'delete_label' => 'Delete :label',
        'show_label' => 'View :label',
    ],

    'error' => 'Your submission contains errors. Please try again',

    'validation' => [
        'integer' => 'This value must be an integer.',
    ],

];

<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Tax;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<Tax>
 */
class TaxFactory extends Factory
{
    protected $model = Tax::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->randomElement(['VAT', 'GST', 'Sales Tax', 'Service Tax', 'Environmental Tax']),
            'code' => $this->faker->unique()->regexify('[A-Z]{2,4}'),
            'type' => $this->faker->randomElement(['percentage', 'fixed_amount']),
            'description' => $this->faker->sentence(),
            'is_active' => $this->faker->boolean(80),
            'is_inclusive' => $this->faker->boolean(30),
            'is_compound' => $this->faker->boolean(20),
            'priority' => $this->faker->numberBetween(0, 10),
            'metadata' => null,
        ];
    }

    /**
     * Indicate that the tax is a percentage type.
     */
    public function percentage(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'percentage',
        ]);
    }

    /**
     * Indicate that the tax is a fixed amount type.
     */
    public function fixedAmount(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'fixed_amount',
        ]);
    }

    /**
     * Indicate that the tax is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the tax is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the tax is inclusive.
     */
    public function inclusive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_inclusive' => true,
        ]);
    }

    /**
     * Indicate that the tax is exclusive.
     */
    public function exclusive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_inclusive' => false,
        ]);
    }

    /**
     * Indicate that the tax is compound.
     */
    public function compound(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_compound' => true,
            'priority' => $this->faker->numberBetween(1, 10),
        ]);
    }

    /**
     * Create a VAT tax.
     */
    public function vat(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Value Added Tax',
            'code' => 'VAT',
            'type' => 'percentage',
            'description' => 'Standard VAT rate',
        ]);
    }

    /**
     * Create a GST tax.
     */
    public function gst(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Goods and Services Tax',
            'code' => 'GST',
            'type' => 'percentage',
            'description' => 'Standard GST rate',
        ]);
    }

    /**
     * Create a sales tax.
     */
    public function salesTax(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Sales Tax',
            'code' => 'ST',
            'type' => 'percentage',
            'description' => 'Standard sales tax rate',
        ]);
    }
}

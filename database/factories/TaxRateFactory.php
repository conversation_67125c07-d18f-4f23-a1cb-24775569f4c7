<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Tax;
use App\Models\TaxRate;
use Illuminate\Database\Eloquent\Factories\Factory;
use Shopper\Core\Models\Currency;
use Shopper\Core\Models\Zone;

/**
 * @extends Factory<TaxRate>
 */
class TaxRateFactory extends Factory
{
    protected $model = TaxRate::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'tax_id' => Tax::factory(),
            'zone_id' => null, // Default to global rate
            'currency_id' => null, // Default to all currencies
            'rate' => $this->faker->randomFloat(4, 0, 30), // 0% to 30%
            'fixed_amount' => null,
            'is_default' => $this->faker->boolean(20),
            'effective_from' => $this->faker->optional(0.3)->dateTimeBetween('-1 year', 'now'),
            'effective_until' => $this->faker->optional(0.2)->dateTimeBetween('now', '+1 year'),
        ];
    }

    /**
     * Indicate that this is a percentage rate.
     */
    public function percentage(float $rate = null): static
    {
        return $this->state(fn (array $attributes) => [
            'rate' => $rate ?? $this->faker->randomFloat(4, 5, 25),
            'fixed_amount' => null,
        ]);
    }

    /**
     * Indicate that this is a fixed amount rate.
     */
    public function fixedAmount(int $amount = null): static
    {
        return $this->state(fn (array $attributes) => [
            'rate' => 0,
            'fixed_amount' => $amount ?? $this->faker->numberBetween(100, 5000), // $1 to $50
        ]);
    }

    /**
     * Indicate that this is a default rate.
     */
    public function default(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_default' => true,
        ]);
    }

    /**
     * Set the tax for this rate.
     */
    public function forTax(Tax $tax): static
    {
        return $this->state(fn (array $attributes) => [
            'tax_id' => $tax->id,
        ]);
    }

    /**
     * Set the zone for this rate.
     */
    public function forZone(Zone $zone): static
    {
        return $this->state(fn (array $attributes) => [
            'zone_id' => $zone->id,
        ]);
    }

    /**
     * Set the currency for this rate.
     */
    public function forCurrency(Currency $currency): static
    {
        return $this->state(fn (array $attributes) => [
            'currency_id' => $currency->id,
        ]);
    }

    /**
     * Set effective dates for this rate.
     */
    public function effectiveBetween(string $from, string $until = null): static
    {
        return $this->state(fn (array $attributes) => [
            'effective_from' => $from,
            'effective_until' => $until,
        ]);
    }

    /**
     * Create a currently effective rate.
     */
    public function currentlyEffective(): static
    {
        return $this->state(fn (array $attributes) => [
            'effective_from' => $this->faker->dateTimeBetween('-6 months', '-1 month'),
            'effective_until' => $this->faker->dateTimeBetween('+1 month', '+6 months'),
        ]);
    }

    /**
     * Create a VAT rate.
     */
    public function vatRate(float $rate = 20.0): static
    {
        return $this->percentage($rate)->state(fn (array $attributes) => [
            'is_default' => true,
        ]);
    }

    /**
     * Create a GST rate.
     */
    public function gstRate(float $rate = 10.0): static
    {
        return $this->percentage($rate)->state(fn (array $attributes) => [
            'is_default' => true,
        ]);
    }

    /**
     * Create a sales tax rate.
     */
    public function salesTaxRate(float $rate = 8.5): static
    {
        return $this->percentage($rate)->state(fn (array $attributes) => [
            'is_default' => true,
        ]);
    }
}

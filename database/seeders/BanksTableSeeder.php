<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Bank;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

final class BanksTableSeeder extends Seeder
{
    protected array $banks;

    public function __construct()
    {
        $this->banks = include __DIR__ . '/../data/banks.php';
    }

    public function run(): void
    {
        Schema::disableForeignKeyConstraints();

        $banks = collect($this->banks)
            ->map(fn ($bank): array => [
                'name' => $bank['name'],
                'code' => $bank['code'],
                'bin' => $bank['bin'],
                'shortName' => $bank['shortName'],
                'logo' => $bank['logo'],
            ])
            ->toArray();

        Bank::query()->insert($banks);

        Schema::enableForeignKeyConstraints();
    }
}

<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Tax;
use App\Models\TaxRate;
use Illuminate\Database\Seeder;
use Shopper\Core\Models\Zone;
use Shopper\Core\Models\Currency;

class TaxSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create basic tax types
        $this->createBasicTaxes();
        
        // Create zone-specific tax rates if zones exist
        $this->createZoneSpecificRates();
    }

    /**
     * Create basic tax types with default rates
     */
    private function createBasicTaxes(): void
    {
        // VAT (Value Added Tax)
        $vat = Tax::create([
            'name' => 'Value Added Tax',
            'code' => 'VAT',
            'type' => 'percentage',
            'description' => 'Standard VAT rate applied to most goods and services',
            'is_active' => true,
            'is_inclusive' => false,
            'is_compound' => false,
            'priority' => 1,
        ]);

        TaxRate::create([
            'tax_id' => $vat->id,
            'rate' => 20.00, // 20% VAT
            'is_default' => true,
        ]);

        // GST (Goods and Services Tax)
        $gst = Tax::create([
            'name' => 'Goods and Services Tax',
            'code' => 'GST',
            'type' => 'percentage',
            'description' => 'Standard GST rate for goods and services',
            'is_active' => true,
            'is_inclusive' => false,
            'is_compound' => false,
            'priority' => 1,
        ]);

        TaxRate::create([
            'tax_id' => $gst->id,
            'rate' => 10.00, // 10% GST
            'is_default' => true,
        ]);

        // Sales Tax
        $salesTax = Tax::create([
            'name' => 'Sales Tax',
            'code' => 'ST',
            'type' => 'percentage',
            'description' => 'Standard sales tax rate',
            'is_active' => true,
            'is_inclusive' => false,
            'is_compound' => false,
            'priority' => 1,
        ]);

        TaxRate::create([
            'tax_id' => $salesTax->id,
            'rate' => 8.50, // 8.5% Sales Tax
            'is_default' => true,
        ]);

        // Environmental Tax (Fixed Amount)
        $envTax = Tax::create([
            'name' => 'Environmental Tax',
            'code' => 'ENV',
            'type' => 'fixed_amount',
            'description' => 'Environmental protection tax on certain products',
            'is_active' => true,
            'is_inclusive' => false,
            'is_compound' => false,
            'priority' => 2,
        ]);

        TaxRate::create([
            'tax_id' => $envTax->id,
            'rate' => 0,
            'fixed_amount' => 50, // $0.50 environmental tax
            'is_default' => true,
        ]);

        // Luxury Tax (Compound Tax)
        $luxuryTax = Tax::create([
            'name' => 'Luxury Tax',
            'code' => 'LUX',
            'type' => 'percentage',
            'description' => 'Additional tax on luxury items, calculated on price + other taxes',
            'is_active' => false, // Disabled by default
            'is_inclusive' => false,
            'is_compound' => true,
            'priority' => 10, // Higher priority for compound calculation
        ]);

        TaxRate::create([
            'tax_id' => $luxuryTax->id,
            'rate' => 5.00, // 5% luxury tax
            'is_default' => true,
        ]);
    }

    /**
     * Create zone-specific tax rates
     */
    private function createZoneSpecificRates(): void
    {
        // Get existing zones
        $zones = Zone::all();
        
        if ($zones->isEmpty()) {
            return;
        }

        // Get existing taxes
        $vat = Tax::where('code', 'VAT')->first();
        $gst = Tax::where('code', 'GST')->first();
        
        if (!$vat || !$gst) {
            return;
        }

        // Create different VAT rates for different zones
        foreach ($zones->take(3) as $index => $zone) {
            $vatRates = [18.00, 22.00, 15.00]; // Different VAT rates
            $gstRates = [12.00, 8.00, 15.00];  // Different GST rates
            
            if (isset($vatRates[$index])) {
                TaxRate::create([
                    'tax_id' => $vat->id,
                    'zone_id' => $zone->id,
                    'rate' => $vatRates[$index],
                    'is_default' => false,
                ]);
            }
            
            if (isset($gstRates[$index])) {
                TaxRate::create([
                    'tax_id' => $gst->id,
                    'zone_id' => $zone->id,
                    'rate' => $gstRates[$index],
                    'is_default' => false,
                ]);
            }
        }
    }
}

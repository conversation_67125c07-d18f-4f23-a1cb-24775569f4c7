<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Shopper\Core\Models\Tax;

class TaxSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $tax = Tax::create([
            'name' => 'None',
            'code' => 'NONE',
            'type' => 'percentage',
            'is_active' => true,
            'is_inclusive' => false,
            'is_compound' => false,
            'priority' => 0,
        ]);

        $tax->taxRates()->create([
            'rate' => 0,
            'fixed_amount' => 0,
            'is_default' => true,
        ]);

        $tax = Tax::create([
            'name' => 'VAT',
            'code' => 'VAT',
            'type' => 'percentage',
            'is_active' => true,
            'is_inclusive' => false,
            'is_compound' => false,
            'priority' => 0,
        ]);

        $tax->taxRates()->create([
            'rate' => 10,
            'fixed_amount' => 0,
            'is_default' => true,
        ]);
    }
}

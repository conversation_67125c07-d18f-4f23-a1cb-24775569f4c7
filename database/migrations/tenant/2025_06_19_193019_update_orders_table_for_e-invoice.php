<?php

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Shopper\Core\Helpers\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table($this->getTableName('orders'), function (Blueprint $table): void {
            $table->after('is_export_invoice', function (Blueprint $table): void {
                $table->string('e_invoice_provider_code')->nullable()->comment('Provider code for e-invoice');
                $table->string('e_invoice_pattern')->nullable()->comment('Pattern for e-invoice');
                $table->string('e_invoice_serial')->nullable()->comment('Serial for e-invoice');
                $table->string('e_invoice_invoice_number')->nullable()->comment('Serial for e-invoice');
                $table->string('e_invoice_authority_issued_code')->nullable()->comment('Invoice number for e-invoice');
            });
        });
    }

    public function down(): void
    {
        Schema::table($this->getTableName('orders'), function (Blueprint $table): void {
            $table->dropColumn([
                'e_invoice_provider_code',
                'e_invoice_pattern',
                'e_invoice_serial',
                'e_invoice_invoice_number',
                'e_invoice_authority_issued_code',
            ]);
        });
    }
};

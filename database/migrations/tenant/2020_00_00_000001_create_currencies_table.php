<?php

declare(strict_types=1);

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Shopper\Core\Helpers\Migration;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create($this->getTableName('currencies'), function (Blueprint $table): void {
            $table->id();
            $table->string('name');
            $table->string('code', 10)->index();
            $table->string('symbol', 25);
            $table->string('format', 50);
            $table->string('exchange_rate')->nullable();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists($this->getTableName('currencies'));
    }
};

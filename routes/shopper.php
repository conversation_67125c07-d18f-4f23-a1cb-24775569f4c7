<?php

declare(strict_types=1);

use App\Http\Controllers\Shopper\DailyReportPrintController;
use App\Http\Controllers\Tenant\PosController;
use App\Http\Middleware\TenantAuthMiddleware;
use App\Livewire\Shopper\Components\Setting\Pages\EInvoiceIndex;
use App\Livewire\Shopper\Pages\Tax\Index as TaxIndex;
use App\Livewire\Shopper\Pages\Tax\Rates as TaxRates;
use Illuminate\Support\Facades\Route;
use Shopper\Feature;
use Stancl\Tenancy\Middleware\InitializeTenancyByDomain;

/*
|--------------------------------------------------------------------------
| Tenant Routes
|--------------------------------------------------------------------------
|
| Here you can register the tenant routes for your application.
| These routes are loaded by the TenantRouteServiceProvider.
|
| Feel free to customize them however you want. Good luck!
|
*/

Route::middleware([
    'web',
    InitializeTenancyByDomain::class,
])->group(function () {
    Route::prefix('setting')->group(function (): void {
        Route::prefix('e-invoice')->group(function (): void {
            Route::get('/', EInvoiceIndex::class)->name('settings.e-invoice');
        });
        // Tax Management Routes
        Route::prefix('taxes')->group(function (): void {
            Route::get('/', TaxIndex::class)->name('settings.taxes.index');
            Route::get('/{tax}/rates', TaxRates::class)->name('settings.taxes.rates');
        });
    });
    if (Feature::enabled('report')) {
        Route::prefix('report')->group(function (): void {
            Route::get('/sale-report', config('shopper.components.report.pages.sale-report'))->name('report.sale-report');
            Route::get('/daily-report', config('shopper.components.report.pages.daily-report'))->name('report.daily-report');

            // Print routes for daily report
            Route::get('/daily-report/print', [DailyReportPrintController::class, 'printReport'])->name('report.daily.print');
            Route::get('/daily-report/print-summary', [DailyReportPrintController::class, 'printSummaryOnly'])->name('report.daily.print-summary');
            Route::get('/daily-report/print-staff', [DailyReportPrintController::class, 'printStaffReport'])->name('report.daily.print-staff');
        });
    }
});

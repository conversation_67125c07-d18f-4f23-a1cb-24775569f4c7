<?php

declare(strict_types=1);

use App\Http\Controllers\Tenant\PosController;
use App\Http\Middleware\CheckTenantVisibility;
use App\Http\Middleware\TenantAuthMiddleware;
use Illuminate\Support\Facades\Route;
use Stancl\Tenancy\Middleware\InitializeTenancyByDomain;

/*
|--------------------------------------------------------------------------
| Tenant Routes
|--------------------------------------------------------------------------
|
| Here you can register the tenant routes for your application.
| These routes are loaded by the TenantRouteServiceProvider.
|
| Feel free to customize them however you want. Good luck!
|
*/

Route::middleware([
    'web',
    InitializeTenancyByDomain::class,
    CheckTenantVisibility::class
])->group(function () {
    Route::get('/', function () {
        return view('tenant.welcome');
    });
    Route::get('/pos', [PosController::class, 'index'])->name('tenant.pos')->middleware([TenantAuthMiddleware::class]);
});

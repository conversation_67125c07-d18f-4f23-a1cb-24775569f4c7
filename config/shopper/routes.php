<?php

declare(strict_types=1);

use Stancl\Tenancy\Middleware\InitializeTenancyByDomain;

return [

    /*
    |--------------------------------------------------------------------------
    | Shopper Routes Middleware
    |--------------------------------------------------------------------------
    |
    | Here you may specify which middleware Shopper will assign to the routes
    | that it registers with the application. If necessary, you may change
    | these middleware but typically this provided default is preferred.
    |
    */

    'middleware' => [
        'universal',
        InitializeTenancyByDomain::class,
    ],

    /*
    |--------------------------------------------------------------------------
    | Shopper Custom backend route file
    |--------------------------------------------------------------------------
    |
    | This value sets the file to load for Shopper admin custom routes.
    | E.g.: base_path('routes/shopper.php')
    |
    */

    'custom_file' => base_path('routes/shopper.php'),
];

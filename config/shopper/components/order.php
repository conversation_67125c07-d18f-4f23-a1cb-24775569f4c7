<?php

declare(strict_types=1);

use App\Livewire\Shopper\Pages\Order\Detail as OrderDetail;
use App\Livewire\Shopper\Pages\Order\Index as OrderIndex;
use Shopper\Livewire;

return [

    /*
    |--------------------------------------------------------------------------
    | Livewire Pages
    |--------------------------------------------------------------------------
    */

    'pages' => [
        'order-index' => OrderIndex::class,
        'order-detail' => OrderDetail::class,
    ],

    /*
    |--------------------------------------------------------------------------
    | Livewire Components
    |--------------------------------------------------------------------------
    */

    'components' => [
        'modals.archived-order' => Livewire\Modals\ArchiveOrder::class,
    ],

];

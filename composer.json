{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "darryldecode/cart": "dev-l12-compatibility", "endroid/qr-code": "^5.0", "filament/filament": "^3.3", "laravel/framework": "^12.0", "laravel/octane": "^2.9", "laravel/tinker": "^2.10.1", "livewire/flux": "^2.1", "livewire/flux-pro": "^2.1", "shopper/framework": "dev-custom-2.x", "stancl/tenancy": "dev-master", "mckenziearts/blade-untitledui-icons": "^1.4", "spatie/laravel-medialibrary": "^11.5", "spatie/laravel-permission": "^6.7", "staudenmeir/laravel-adjacency-list": "^1.0"}, "require-dev": {"fakerphp/faker": "^1.23", "laravel/pail": "^1.2.2", "laravel/pint": "^1.16", "laravel/sail": "^1.41", "mockery/mockery": "^1.4", "nunomaduro/collision": "^7.10.0|^8.1.1", "pestphp/pest": "^3.5", "pestphp/pest-plugin-laravel": "^3.0", "bacon/bacon-qr-code": "^2.0", "codeat3/blade-phosphor-icons": "^2.3", "codewithdennis/filament-select-tree": "^3.1", "jaocero/radio-deck": "^1.2", "blade-ui-kit/blade-heroicons": "^2.5", "danharrin/livewire-rate-limiting": "^0.3|^1.0|^2.0", "doctrine/dbal": "^3.6", "filament/filament": "^3.3", "filament/spatie-laravel-media-library-plugin": "^3.3", "gehrisandro/tailwind-merge-laravel": "^1.3", "jenssegers/agent": "^2.6", "larastan/larastan": "^2.9|^3.0", "laravel/framework": "^10.0|^11.0|^12.0", "pragmarx/google2fa": "^8.0", "orchestra/testbench": "^8.20|^9.0|^10.0", "pestphp/pest-plugin-livewire": "^3.0", "phpstan/phpstan": "^1.8|^2.0", "rector/rector": "^1.2|^2.0", "spatie/laravel-livewire-wizard": "^2.2", "stevebauman/location": "^7.2", "spatie/laravel-package-tools": "^1.15", "symplify/monorepo-builder": "^10.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/helpers.php", "app/Helpers/DateHelper.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"], "test": ["@php artisan config:clear --ansi", "@php artisan test"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true, "repositories": {"flux-pro": {"type": "composer", "url": "https://composer.fluxui.dev"}, "laravelshoppingcart": {"type": "vcs", "url": "https://github.com/laravel-shift/laravelshoppingcart.git"}, "shopper": {"type": "vcs", "url": "https://github.com/thanhtrungit97/shopper.git"}}}
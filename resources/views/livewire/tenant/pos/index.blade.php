<div class="w-full">
    <nav class="bg-gray-800">
        <div class="mx-auto px-4 sm:px-6 lg:px-8 w-full">
            <div class="flex h-16 items-center justify-between">
                <div class="flex items-center">
                    <div class="flex gap-2 items-center">
                        <img class="size-8" src="{{ asset('cpanel/images/shopper-icon.svg') }}" alt="Your Company">
                        <h1 class="text-white font-bold text-xl">{{ shopper_setting('name') }}</h1>
                    </div>
                    <div class="hidden md:block">
                        <div class="ml-10 flex items-baseline space-x-4">
                            <!-- Current: "bg-gray-900 text-white", Default: "text-gray-300 hover:bg-gray-700 hover:text-white" -->
                            <a href="{{ route('shopper.dashboard') }}"
                                class="rounded-md px-3 py-2 text-sm font-medium text-gray-300 hover:bg-gray-700 hover:text-white"><PERSON><PERSON><PERSON><PERSON>
                                lý cửa hàng</a>
                            <a href="{{ route('shopper.orders.index') }}"
                                class="rounded-md px-3 py-2 text-sm font-medium text-gray-300 hover:bg-gray-700 hover:text-white">Đơn
                                hàng</a>
                            <flux:modal.trigger name="orders-draft">
                                <button
                                    class="rounded-md px-3 py-2 text-sm font-medium text-gray-300 hover:bg-gray-700 hover:text-white">
                                    Đơn đang chờ
                                    @if($pendingOrders->count() > 0)
                                    <flux:badge color="zinc" class="bg-red-500! text-white! rounded-full! h-5!">{{ $pendingOrders->count() }}</flux:badge>
                                    @endif
                                </button>
                            </flux:modal.trigger>
                        </div>
                    </div>
                </div>
                <div class="hidden md:block">
                    <div class="ml-4 flex items-center md:ml-6">
                        <livewire:shopper-account.dropdown />
                    </div>
                </div>
                <div class="-mr-2 flex md:hidden">
                    <!-- Mobile menu button -->
                    <button type="button"
                        class="relative inline-flex items-center justify-center rounded-md bg-gray-800 p-2 text-gray-400 hover:bg-gray-700 hover:text-white focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-800 focus:outline-hidden"
                        aria-controls="mobile-menu" aria-expanded="false">
                        <span class="absolute -inset-0.5"></span>
                        <span class="sr-only">Open main menu</span>
                        <!-- Menu open: "hidden", Menu closed: "block" -->
                        <svg class="block size-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                            stroke="currentColor" aria-hidden="true" data-slot="icon">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
                        </svg>
                        <!-- Menu open: "block", Menu closed: "hidden" -->
                        <svg class="hidden size-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                            stroke="currentColor" aria-hidden="true" data-slot="icon">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <flux:modal name="orders-draft" variant="flyout">
        <div class="space-y-6">
            <div>
                <flux:heading size="lg">Đơn hàng đang chờ</flux:heading>
            </div>
            <div class="flex-1 overflow-y-auto">
                @if($pendingOrders->isEmpty())
                <div class="flex flex-col items-center justify-center h-full text-center">
                    <svg class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">Không có đơn hàng nào</h3>
                </div>
                @else
                <ul role="list" class="divide-y divide-gray-200">
                    @foreach($pendingOrders as $order)
                    <li class="py-4">
                        <div class="flex items-center space-x-4">
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 truncate">
                                    Mã đơn: {{ $order->number }}
                                </p>
                                <p class="text-sm text-gray-500 truncate">
                                    {{ $order->created_at->format('d/m/Y H:i') }}
                                </p>
                                <p class="text-sm font-medium text-gray-900">
                                    {{ shopper_money_format($order->total(), currency: current_currency()) }}
                                </p>
                                <p class="text-sm text-gray-500">
                                    {{ $order->items->count() }} sản phẩm
                                </p>
                            </div>
                            <div>
                                <flux:button icon="shopping-cart" class="bg-white! border-blue-500! text-blue-500!" size="sm" variant="primary" wire:click="loadOrderToCart({{ $order->id }})">
                                    Tiếp tục đơn hàng
                                </flux:button>
                            </div>
                        </div>
                    </li>
                    @endforeach
                </ul>
                @endif
            </div>
        </div>
    </flux:modal>
    <div class="flex-1 grid grid-cols-12 h-full">
        <!-- Center: Product Panel -->
        <section class="col-span-8 space-y-4 overflow-y-auto">
            <div class="sticky top-0 bg-white z-10 gap-4 p-6 ">
                <div class="flex gap-4 pb-4">
                    <flux:input icon="magnifying-glass" placeholder="Tìm sản phẩm" wire:model.live="searchProduct">
                        <x-slot name="iconTrailing">
                            <flux:button size="sm" variant="subtle" icon="qr-code" class="-mr-1" />
                        </x-slot>
                    </flux:input>
                    {{--
                    <livewire:tenant.pos.select-table /> --}}

                </div>
                <!-- Categories -->
                <div class="flex gap-3 overflow-x-auto pb-2">
                    <flux:radio.group label="Danh mục sản phẩm" variant="cards" :indicator="false"
                        wire:model.live="selectedCategory">
                        <flux:radio class="min-w-40 text-center p-2!" value="all" label="Tất cả" />
                        @foreach($categories as $category)
                        <flux:radio class="min-w-40 text-center p-2!" value="{{$category->id}}"
                            label="{{$category->name}}" />
                        @endforeach
                    </flux:radio.group>

                </div>
            </div>
            <div class="p-4">
                <!-- Product Grid -->
                @if ($products->isNotEmpty())
                <div class="grid grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-6">
                    @foreach ($products as $product)
                    <x-products.index :product="$product" />
                    @endforeach
                </div>
                @else
                <div class="flex flex-col items-center justify-center py-12 text-center">
                    <flux:icon.document-magnifying-glass class="h-12 w-12 text-gray-400" />
                    <h3 class="mt-4 text-lg font-medium text-gray-900">Không tìm thấy sản phẩm</h3>
                    <p class="mt-1 text-sm text-gray-500">
                        @if($searchProduct)
                            Không tìm thấy sản phẩm phù hợp với từ khóa "{{ $searchProduct }}".
                        @elseif($selectedCategory != 'all')
                            Không có sản phẩm nào trong danh mục này.
                        @else
                            Chưa có sản phẩm nào được thêm vào hệ thống.
                        @endif
                    </p>
                    <div class="mt-6">
                        @if($searchProduct || $selectedCategory != 'all')
                            <flux:button wire:click="resetFilters" variant="primary" class="bg-white! border-red-500! text-red-500!">
                                Xóa bộ lọc
                            </flux:button>
                        @endif
                    </div>
                </div>
                @endif
            </div>
        </section>

        <!-- Right: Cart Panel -->
        <aside class="col-span-4 bg-white border-l border-gray-200 overflow-y-auto">
            <flux:card class="rounded-none! p-0! border-none!">
                <div class="flex justify-between items-center p-2 border-b border-gray-200">
                    <flux:heading size="lg" class="font-bold!">Giỏ hàng</flux:heading>
                </div>
                <div class="p-2 border-b border-gray-200">
                    <flux:heading size="lg" class="font-bold!">Khách hàng</flux:heading>
                    <div class="w-full mt-2">
                        <flux:select wire:model.live="customerId" variant="listbox" searchable class="w-full">
                            @if ($this->customers)
                            @foreach ($this->customers as $customer)
                            <flux:select.option value="{{ $customer->id }}" wire:key="{{ $customer->id }}">
                                ID: {{ $customer->id }} - {{ $customer->first_name }} {{ $customer->last_name }} | SĐT:
                                {{ $customer->phone_number }}
                            </flux:select.option>
                            @endforeach
                            @endif
                        </flux:select>
                    </div>
                    <div class="mt-2 flex justify-end">
                        <livewire:tenant.pos.create-customer />
                    </div>

                </div>
                <div class="">
                    <div class="flex justify-between items-center p-2 border-b border-gray-200">
                        <flux:heading size="lg" class="font-bold!">Sản phẩm trong giỏ hàng</flux:heading>
                        <flux:badge variant="solid" color="sky">{{ $cartItems->count() }} sản phẩm</flux:badge>
                    </div>
                    @if ($cartItems->isNotempty())
                    <div class="px-2 flex flex-col gap-2 my-2 divide-y divide-gray-200 divide-dashed">
                        @foreach ($cartItems as $cartItem)
                        <x-cart.item :cartItem="$cartItem" />
                        @endforeach
                    </div>
                    @else
                        <div class="flex flex-col justify-center items-center py-5">
                            <flux:heading size="lg">Giỏ hàng đang trống</flux:heading>
                            <flux:subheading>Thêm sản phẩm vào giỏ hàng để bắt đầu thanh toán</flux:subheading>
                        </div>
                    @endif
                </div>
            </flux:card>


            <div class="sticky bottom-0 bg-white z-10 flex gap-2 flex-col p-4">

                <flux:accordion>
                    <flux:accordion.item>
                        <flux:accordion.heading>
                            <div class="flex gap-4 justify-between">
                                <flux:heading class="font-bold" size="lg">Tổng tiền</flux:heading>
                                <flux:heading size="lg">{{ shopper_money_format($total, currency: current_currency()) }}
                                </flux:heading>
                            </div>
                        </flux:accordion.heading>

                        <flux:accordion.content class="flex flex-col divide-y divide-gray-200 pr-8">
                            <div class="flex gap-4 justify-between">
                                <flux:heading>Tiền hàng</flux:heading>
                                <flux:heading class="font-bold">{{ shopper_money_format($subTotal, currency:
                                    current_currency()) }}</flux:heading>
                            </div>
                        </flux:accordion.content>
                    </flux:accordion.item>

                </flux:accordion>

                <livewire:tenant.pos.pay :customerId="$customerId" />
            </div>

        </aside>
    </div>
    <!-- Order Completed Modal -->
    <flux:modal name="order-completed-modal" class="w-full max-w-md" wire:ignore>
        <div class="space-y-6">
            <div>
                <flux:heading size="lg">Đơn hàng đã được tạo thành công.</flux:heading>
            </div>

            <div id="completed-order-details" class="space-y-4">
                <div class="grid grid-cols-2 gap-2">
                    <div class="text-gray-500">Mã đơn hàng:</div>
                    <div id="completed-order-number" class="font-medium"></div>

                    <div class="text-gray-500">Ngày tạo:</div>
                    <div id="completed-order-date" class="font-medium"></div>

                    <div class="text-gray-500">Khách hàng:</div>
                    <div id="completed-customer-name" class="font-medium"></div>

                    <div class="text-gray-500">Số điện thoại:</div>
                    <div id="completed-customer-phone" class="font-medium"></div>

                    <div class="text-gray-500">Tổng tiền:</div>
                    <div id="completed-order-total" class="font-medium"></div>

                    <div class="text-gray-500">Phương thức thanh toán:</div>
                    <div id="completed-payment-method" class="font-medium"></div>
                </div>

                <!-- Bank transfer details (shown only for bank transfers) -->
                <div id="bank-transfer-details" class="hidden mt-4 p-4 bg-gray-50 rounded-lg">
                    <div class="text-center mb-3">
                        <div class="font-medium">Thông tin chuyển khoản</div>
                    </div>
                    <div class="grid grid-cols-2 gap-2">
                        <div class="text-gray-500">Ngân hàng:</div>
                        <div id="bank-name" class="font-medium"></div>

                        <div class="text-gray-500">Tên tài khoản:</div>
                        <div id="bank-account-name" class="font-medium"></div>

                        <div class="text-gray-500">Số tài khoản:</div>
                        <div id="bank-account-number" class="font-medium"></div>

                        <div class="text-gray-500">Nội dung CK:</div>
                        <div id="bank-memo" class="font-medium"></div>
                    </div>

                    <!-- QR Code -->
                    <div class="mt-4 flex justify-center">
                        <img id="qr-code-image" src="" alt="QR Code" class="w-48 h-48">
                    </div>
                </div>
            </div>

            <div class="flex justify-end space-x-3">
                <flux:button x-on:click="$flux.modal('order-completed-modal').close()">
                    Đóng
                </flux:button>
            </div>
        </div>
    </flux:modal>

    @script
    <script>
        $wire.on('order-completed', (target) => {
            // @this.on('order-completed', (target) => {
                let data = target[0];
                // Format currency
                const formatCurrency = (amount) => {
                    return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(amount);
                };
                
                // Fill order details
                document.getElementById('completed-order-number').textContent = data.order_number;
                document.getElementById('completed-order-date').textContent = data.order_date;
                document.getElementById('completed-customer-name').textContent = data.customer_name;
                document.getElementById('completed-customer-phone').textContent = data.customer_phone;
                document.getElementById('completed-order-total').textContent = formatCurrency(data.total);
                document.getElementById('completed-payment-method').textContent = data.payment_method;
                
                // Handle bank transfer details
                const bankTransferDetails = document.getElementById('bank-transfer-details');
                if (data.qrImage) {
                    // Show bank transfer details
                    bankTransferDetails.classList.remove('hidden');
                    
                    // Fill bank details
                    document.getElementById('bank-name').textContent = data.bank.shortName;
                    document.getElementById('bank-account-name').textContent = data.bank.account_name;
                    document.getElementById('bank-account-number').textContent = data.bank.account_number;
                    document.getElementById('bank-memo').textContent = data.bank.memo;
                    
                    // Set QR code image
                    document.getElementById('qr-code-image').src = data.qrImage;
                } else {
                    // Hide bank transfer details
                    bankTransferDetails.classList.add('hidden');
                }
                
                // Add event listener for print receipt button
                document.getElementById('print-receipt-btn').addEventListener('click', () => {
                    // Trigger the same print function that was used earlier
                    @this.call('printTemp');
                });
            // });
        });
    </script>
    @endscript
</div>

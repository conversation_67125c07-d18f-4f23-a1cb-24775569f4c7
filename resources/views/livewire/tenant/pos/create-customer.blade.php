<div>
    <flux:modal.trigger name="create-customer">
        <flux:button icon="plus" class="border-blue-300! text-blue-500!">Tạo kh<PERSON>ch hàng mới</flux:button>
    </flux:modal.trigger>

    <flux:modal name="create-customer" class="w-full">
        <div class="space-y-6">
            <div>
                <flux:heading size="lg">Tạo khách hàng mới</flux:heading>
            </div>

            <div class="grid grid-cols-2 gap-4">
            <flux:field>
                <flux:label>Họ <span class="text-red-400">*</span></flux:label>
                <flux:input wire:model='first_name' placeholder="Họ" />
                <flux:error name="first_name" />
            </flux:field>
            <flux:field>
                <flux:label>Tên <span class="text-red-400">*</span></flux:label>
                <flux:input wire:model='last_name' placeholder="Tên" />
                <flux:error name="last_name" />
            </flux:field>
            <flux:field>
                <flux:label><PERSON><PERSON> điện thoại <span class="text-red-400">*</span></flux:label>
                <flux:input wire:model='phone_number' placeholder="Số điện thoại" />
                <flux:error name="phone_number" />
            </flux:field>
            <flux:field>
                <flux:label>Địa chỉ email <span class="text-red-400">*</span></flux:label>
                <flux:input wire:model='email' placeholder="<EMAIL>" />
                <flux:error name="email" />
            </flux:field>
            </div>
            
            

            <div class="flex">
                <flux:spacer />
                <flux:button icon="plus" wire:click="createCustomer" variant="primary" class="bg-white! border-blue-500! text-blue-500!">Tạo mới</flux:button>
            </div>
        </div>
    </flux:modal>
</div>

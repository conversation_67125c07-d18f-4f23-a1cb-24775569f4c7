<div>
    <flux:modal.trigger name="edit-profile">
        <flux:button icon="map-pin">Bàn 2</flux:button>
    </flux:modal.trigger>

    <flux:modal name="edit-profile" variant="flyout" class="!w-[80%]">



        <flux:tab.group >
            <flux:tabs wire:model="tab">
                <flux:tab name="profile">Tầng 1</flux:tab>
                <flux:tab name="account">Tầng 2</flux:tab>
                <flux:tab name="billing">Ban công</flux:tab>
            </flux:tabs>

            <flux:tab.panel name="profile">
                <flux:radio.group  variant="cards" :indicator="false" class="max-sm:flex-col flex-wrap">
                    <flux:radio value="fast" icon="cube" label="Bàn 2" description="Đang sử dụng" class="min-w-[200px] !bg-green-100"/>
                    @for($i = 1; $i <= 28; $i++)
                    <flux:radio value="standard" icon="truck" label="Bàn {{$i}}" description="Đang trống" class="min-w-[200px]"/>
                    @endfor

                </flux:radio.group>
            </flux:tab.panel>
            <flux:tab.panel name="account">...</flux:tab.panel>
            <flux:tab.panel name="billing">...</flux:tab.panel>
        </flux:tab.group>




    </flux:modal>
</div>

<div>
    <div class="grid grid-cols-2 md:grid-cols-2 gap-2">
        <!-- Hide "In tạm tính" button on mobile -->
        <flux:button icon="printer" class="hidden md:flex !h-10 md:!h-12 bg-white! border-gray-500! text-gray-500!" wire:click="printTemp">In tạm tính</flux:button>
        <flux:button wire:click="clearCart" class="!h-10 md:!h-12 bg-white! border-red-500! text-red-500! text-sm md:text-base">Huỷ</flux:button>
        <flux:button icon="clock" class="!h-10 md:!h-12 bg-white! border-yellow-500! text-yellow-500! text-sm md:text-base" wire:click="saveDraft">Lưu nháp</flux:button>
        <flux:modal.trigger name="payment">
            <flux:button icon="shopping-cart" variant="primary" class="!h-10 md:!h-12 bg-white! border-blue-500! text-blue-500! text-sm md:text-base">Thanh toán</flux:button>
        </flux:modal.trigger>
    </div>

    <!-- Print Template (hidden) -->
    <div id="print-template-tmp" class="hidden">
        <div class="print-bill">
            <style>
                @page {
                    size: 80mm auto;  /* Width: 80mm, Height: auto */
                    margin: 0mm;      /* Remove default margins */
                }
                
                @media print {
                    html, body {
                        width: 80mm;
                        margin: 0;
                        padding: 0;
                        font-family: 'Arial', monospace;
                        font-size: 12px;
                    }
                    
                    .print-bill {
                        width: 72mm;  /* Actual print area (80mm - margins) */
                        padding: 4mm;
                    }
                    
                    .store-info {
                        text-align: center;
                        margin-bottom: 8px;
                    }
                    
                    .store-name {
                        font-size: 14px;
                        font-weight: bold;
                    }
                    
                    .bill-title {
                        text-align: center;
                        font-size: 14px;
                        font-weight: bold;
                        margin: 8px 0;
                        border-bottom: 1px dashed #000;
                        padding-bottom: 4px;
                    }
                    
                    .bill-date {
                        text-align: right;
                        font-size: 10px;
                        margin-bottom: 8px;
                    }
                    
                    .bill-items {
                        width: 100%;
                        border-collapse: collapse;
                        font-size: 10px;
                    }
                    
                    .bill-items th, .bill-items td {
                        text-align: left;
                        padding: 2px 0;
                    }
                    
                    .bill-items .price {
                        text-align: right;
                    }
                    
                    .bill-total {
                        margin-top: 8px;
                        text-align: right;
                        font-size: 12px;
                        font-weight: bold;
                        border-top: 1px dashed #000;
                        padding-top: 4px;
                    }
                    
                    .bill-footer {
                        margin-top: 16px;
                        text-align: center;
                        font-size: 10px;
                    }
                    
                    /* Ensure no page breaks inside items */
                    tr, td, th, div {
                        page-break-inside: avoid;
                    }

                    .bill-items thead tr th {
                        font-size: 14px;
                    }
                    #print-items td {
                        font-size: 14px;
                    }
                }
            </style>
            <div class="store-info">
                <div class="store-name" id="print-store-name"></div>
                <div id="print-store-address"></div>
                <div id="print-store-phone"></div>
            </div>
            <div>
                <div>
                    Thu ngân: {{ auth()->user()->first_name }} {{ auth()->user()->last_name }}
                </div>
                <div>
                    Ngày giờ: <span id="print-date"></span>
                </div>
            </div>
            <div class="bill-title">HOÁ ĐƠN TẠM TÍNH</div>
            <table class="bill-items">
                <thead>
                    <tr>
                        <th>Sản phẩm</th>
                        <th>SL</th>
                        <th class="price">Đơn giá</th>
                        <th class="price">T.Tiền</th>
                    </tr>
                </thead>
                <tbody id="print-items">
                    <!-- Items will be inserted here -->
                </tbody>
            </table>
            <div class="bill-total">
                <div>Tạm tính: <span id="print-subtotal"></span></div>
                <div>Tiền thuế: <span id="print-tax-amount"></span></div>
                <div>Tổng thanh toán: <span id="print-total"></span></div>
            </div>
            <div class="bill-footer">
                Cảm ơn quý khách đã mua hàng!
            </div>
            <div class="bill-footer">
                Powered by TinoPOS
            </div>
        </div>
    </div>
    <div id="print-template-final" class="hidden">
        <div class="print-bill">
            <style>
                @page {
                    size: 80mm auto;  /* Width: 80mm, Height: auto */
                    margin: 0mm;      /* Remove default margins */
                }
                
                @media print {
                    html, body {
                        width: 80mm;
                        margin: 0;
                        padding: 0;
                        font-family: 'Arial', monospace;
                        font-size: 12px;
                    }
                    
                    .print-bill {
                        width: 72mm;  /* Actual print area (80mm - margins) */
                        padding: 4mm;
                    }
                    
                    .store-info {
                        text-align: center;
                        margin-bottom: 8px;
                    }
                    
                    .store-name {
                        font-size: 14px;
                        font-weight: bold;
                    }
                    
                    .bill-title {
                        text-align: center;
                        font-size: 14px;
                        font-weight: bold;
                        margin: 8px 0;
                        border-bottom: 1px dashed #000;
                        padding-bottom: 4px;
                    }
                    
                    .bill-date {
                        text-align: right;
                        font-size: 10px;
                        margin-bottom: 8px;
                    }
                    
                    .bill-items {
                        width: 100%;
                        border-collapse: collapse;
                        font-size: 10px;
                    }
                    
                    .bill-items th, .bill-items td {
                        text-align: left;
                        padding: 2px 0;
                    }
                    
                    .bill-items .price {
                        text-align: right;
                    }
                    
                    .bill-total {
                        margin-top: 8px;
                        text-align: right;
                        font-size: 12px;
                        font-weight: bold;
                        border-top: 1px dashed #000;
                        padding-top: 4px;
                    }
                    
                    .bill-footer {
                        margin-top: 16px;
                        text-align: center;
                        font-size: 10px;
                    }
                    
                    /* Ensure no page breaks inside items */
                    tr, td, th, div {
                        page-break-inside: avoid;
                    }
                    .bill-items thead tr th {
                        font-size: 14px;
                    }
                    #print-items td {
                        font-size: 14px;
                    }
                }
            </style>
            <div class="store-info">
                <div class="store-name" id="print-store-name"></div>
                <div id="print-store-address"></div>
                <div id="print-store-phone"></div>
            </div>
            <div>
                <div>
                    Thu ngân: {{ auth()->user()->first_name }} {{ auth()->user()->last_name }}
                </div>
                <div>
                    Khách hàng: <span id="print-customer"></span>
                </div>
                <div>
                    Ngày giờ: <span id="print-date"></span>
                </div>
                <div>
                    Phương thức thanh toán: <span id="print-payment-name"></span>
                </div>
                <div>
                    Mã HD: <span id="print-order-number"></span>
                </div>
            </div>
            <div class="bill-title">HOÁ ĐƠN MUA HÀNG</div>
            <table class="bill-items">
                <thead>
                    <tr>
                        <th>Sản phẩm</th>
                        <th>SL</th>
                        <th class="price">Đơn giá</th>
                        <th class="price">T.Tiền</th>
                    </tr>
                </thead>
                <tbody id="print-items">
                    <!-- Items will be inserted here -->
                </tbody>
            </table>
            <div class="bill-total">
                <div>Tạm tính: <span id="print-subtotal"></span></div>
                <div>Tiền thuế: <span id="print-tax-amount"></span></div>
                <div>Tổng thanh toán: <span id="print-total"></span></div>
            </div>
            <div style="text-align: center; margin-top: 16px;" id="print-bank-info">
               
            </div>
            <div class="bill-footer">
                Cảm ơn quý khách đã mua hàng!
            </div>
            <div class="bill-footer">
                Powered by TinoPOS
            </div>
            <div id="print-e-invoice-info" style="margin-top: 16px;">
                
            </div>
        </div>
    </div>

    <!-- Hidden iframe for printing -->
    <iframe id="print-iframe" style="height:0px; width:0px; position: absolute; top: -10px; left: -10px;"></iframe>

    <!-- JavaScript for printing -->
    <script>
        document.addEventListener('livewire:initialized', () => {
            @this.on('print-bill', (target) => {
                let data = target[0];
                
                // Create a copy of the print template
                const printContent = document.getElementById('print-template-tmp').cloneNode(true);
                
                // Fill in the template with data
                printContent.querySelector('#print-store-name').textContent = data.store.name;
                printContent.querySelector('#print-store-address').textContent = data.store.address;
                printContent.querySelector('#print-store-phone').textContent = data.store.phone;
                printContent.querySelector('#print-date').textContent = data.date;
                
                // Format currency
                const formatCurrency = (amount) => {
                    return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(amount);
                };
                
                // Fill items
                const itemsContainer = printContent.querySelector('#print-items');
                itemsContainer.innerHTML = '';
                
                Object.values(data.items).forEach((item, index) => {
                    const row = document.createElement('tr');
                    // Truncate long product names to fit on 80mm paper
                    const productName = item.name.length > 20 ? item.name.substring(0, 18) + '...' : item.name;
                    
                    row.innerHTML = `
                        <td>${index + 1 }. ${productName}</td>
                        <td>${item.quantity}</td>
                        <td class="price">${formatCurrency(item.price)}</td>
                        <td class="price">${formatCurrency(item.price * item.quantity)}</td>
                    `;
                    itemsContainer.appendChild(row);
                });
                
                // Set totals
                printContent.querySelector('#print-subtotal').textContent = formatCurrency(data.subTotal);
                printContent.querySelector('#print-total').textContent = formatCurrency(data.total);
                printContent.querySelector('#print-tax-amount').textContent = formatCurrency(data.totalTax);
                
                // Get the iframe
                const iframe = document.getElementById('print-iframe');
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                
                // Write the content to the iframe
                iframeDoc.open();
                iframeDoc.write('<html><head><title>Hoá đơn tạm tính</title></head><body>');
                iframeDoc.write(printContent.innerHTML);
                iframeDoc.write('</body></html>');
                iframeDoc.close();
                
                // Wait for content to load then print
                setTimeout(() => {
                    iframe.contentWindow.focus();
                    iframe.contentWindow.print();
                }, 250);
            });
            @this.on('print-bill-final', (target) => {
                let data = target[0];
                
                // Create a copy of the print template
                const printContent = document.getElementById('print-template-final').cloneNode(true);
                
                // Fill in the template with data
                printContent.querySelector('#print-store-name').textContent = data.store.name;
                printContent.querySelector('#print-store-address').textContent = data.store.address;
                printContent.querySelector('#print-store-phone').textContent = data.store.phone;
                printContent.querySelector('#print-date').textContent = data.date;
                printContent.querySelector('#print-customer').textContent = data.customer;
                printContent.querySelector('#print-payment-name').textContent = data.payment.title;
                printContent.querySelector('#print-order-number').textContent = data.order_number;
                if (data.bank) {
                    printContent.querySelector('#print-bank-info').innerHTML = '<div><span id="print-bank-name">'+data.bank.shortName+'</span></div>' +
                    '<div>Chủ tài khoản: <span id="print-bank-account-name">'+data.payment.bank_account_name+'</span></div>' +
                    '<div>Số tài khoản: <span id="print-bank-account-number">'+data.payment.bank_account_number+'</span></div>' +
                    '<div>Nội dung chuyển khoản: <span id="print-bank-memo">'+data.memo+'</span></div>' +
                    '<img id="print-qr-code" src="'+data.qrImage+'" alt="QR Code" style="width: 50%;"/>';
                }
                
                // printContent.querySelector('#print-qr-code').src = data.qrImage;
                // printContent.querySelector('#print-bank-name').textContent = data.bank.shortName;
                // printContent.querySelector('#print-bank-account-name').textContent = data.payment.bank_account_name;
                // printContent.querySelector('#print-bank-account-number').textContent = data.payment.bank_account_number;
                // printContent.querySelector('#print-bank-memo').textContent = data.memo;
                
                // Format currency
                const formatCurrency = (amount) => {
                    return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(amount);
                };
                
                // Fill items
                const itemsContainer = printContent.querySelector('#print-items');
                itemsContainer.innerHTML = '';
                
                Object.values(data.items).forEach((item, index) => {
                    const row = document.createElement('tr');
                    // Truncate long product names to fit on 80mm paper
                    const productName = item.name.length > 20 ? item.name.substring(0, 18) + '...' : item.name;
                    
                    row.innerHTML = `
                        <td>${index + 1 }. ${productName}</td>
                        <td>${item.quantity}</td>
                        <td class="price">${formatCurrency(item.price)}</td>
                        <td class="price">${formatCurrency(item.price * item.quantity)}</td>
                    `;
                    itemsContainer.appendChild(row);
                });
                
                // Set totals
                printContent.querySelector('#print-subtotal').textContent = formatCurrency(data.subTotal);
                printContent.querySelector('#print-total').textContent = formatCurrency(data.total);
                printContent.querySelector('#print-tax-amount').textContent = formatCurrency(data.totalTax);
                if (data.e_invoice_info) {
                    printContent.querySelector('#print-e-invoice-info').innerHTML = 
                    '<div>Mã CQT: <span id="print-e-invoice-number">'+data.e_invoice_info.authority_issued_code+'</span></div>'+
                    '<div>Tra cứu: <span>'+data.e_invoice_info.invoice_check_url+'</span></div>' +
                    '<div>Mã tra cứu: <span>'+data.e_invoice_info.fkey+'</span></div>';
                }
                
                // Get the iframe
                const iframe = document.getElementById('print-iframe');
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                
                // Write the content to the iframe
                iframeDoc.open();
                iframeDoc.write('<html><head><title>Hoá đơn mua hàng</title></head><body>');
                iframeDoc.write(printContent.innerHTML);
                iframeDoc.write('</body></html>');
                iframeDoc.close();
                
                // Wait for content to load then print
                setTimeout(() => {
                    iframe.contentWindow.focus();
                    iframe.contentWindow.print();
                }, 250);
            });
        });
    </script>

    <flux:modal name="payment" variant="flyout" class="!min-w-[90%]"
                x-data="{
            total: $wire.total,
            paid: $wire.total,
            maxPaid: 999999999,
            denominations: [1000, 2000, 5000, 10000, 20000, 50000, 100000, 200000, 500000],
            get paidNumber() {
                return Number(this.paid);
            },
            get change() {
                return this.paidNumber - this.total;
            },
            get suggestions() {
                return this.denominations
                    .map(d => d * Math.ceil(this.total / d))
                    .filter((v, i, arr) => arr.indexOf(v) === i && v >= this.total);
            },
            triggerMask() {
                this.$nextTick(() => {
                    this.$refs.moneyInput?.dispatchEvent(new Event('input'));
                });
            },
            add(val) {
                const newVal = this.paid + val;
                const numericVal = Number(newVal.replace(/\D/g, ''));
                if (numericVal <= this.maxPaid) {
                    this.paid = newVal;
                    this.triggerMask();
                }
            },
            clear() {
                this.paid = '';
                this.triggerMask();
            },
            back() {
                this.paid = this.paid.slice(0, -1);
                this.triggerMask();
            }
        }"
        x-init="
            $watch('$wire.total', value => {
                total = value;
                paid = value;
            })
            "
            
        >

        <div class="grid grid-cols-2 gap-10 mt-5">
            <div class="col-span-1">
                <!-- Tổng kết đơn hàng -->
                <div class="divide-y divide-gray-200 gap-2 flex flex-col mb-4 divide-dashed">
                    <div class="flex justify-between py-2">
                        <flux:heading>Tạm tính</flux:heading>
                        <flux:heading class="font-bold">{{ shopper_money_format($subTotal, currency: current_currency()) }}</flux:heading>
                    </div>
                    @if($totalTax > 0)
                        <div class="flex justify-between py-2">
                            <flux:heading>Tiền thuế</flux:heading>
                            <flux:heading class="font-bold">{{ shopper_money_format($totalTax, currency: current_currency()) }}</flux:heading>
                        </div>
                    @else
                        <div class="flex justify-between py-2">
                            <flux:heading>Tiền thuế</flux:heading>
                            <flux:heading class="font-bold">{{ shopper_money_format(0, currency: current_currency()) }}</flux:heading>
                        </div>
                    @endif
                    <div class="flex justify-between py-2">
                        <flux:heading class="font-bold" size="lg">Tổng tiền</flux:heading>
                        <flux:heading size="lg" class="text-blue-600 font-bold text-lg">{{ shopper_money_format($total, currency: current_currency()) }}</flux:heading>
                    </div>
                    <div class="flex justify-between py-2 items-center">
                        <flux:heading class="font-bold" size="lg">Tiền khách trả</flux:heading>
                        <p class="text-right w-auto font-semibold text-lg text-black" x-text="Intl.NumberFormat('vi-VN').format(paidNumber) + 'đ'"></p>
                    </div>

                    <!-- Tiền thừa -->
                    <div class="flex justify-between py-2 items-center  pt-4">
                        <flux:heading class="font-bold" size="lg">Tiền thừa</flux:heading>
                        <p class="text-lg font-bold text-green-600" x-text="Intl.NumberFormat('vi-VN').format(change > 0 ? change : 0) + 'đ'"></p>
                    </div>
                </div>

                <!-- Phương thức thanh toán -->
                <flux:radio.group 
                label="Chọn phương thức thanh toán"
                wire:model="payment_method_id" variant="cards" :indicator="false" class="mb-4">
                    @if ($payments)
                        @foreach ($payments as $payment)
                        <flux:radio  value="{{ $payment->id }}">
                            <div class="flex gap-2 justify-center items-center">
                                @if ($payment->logo)
                                    <img src="{{ tenant_asset($payment->logo) }}" alt="" class=" rounded-full w-[32px] h-[32px]">
                                @endif
                                <flux:heading class="leading-4">{{ $payment->title }}</flux:heading>
                            </div>
                        </flux:radio>
                        @endforeach
                    @endif
                </flux:radio.group>

                <!-- Hóa đơn -->
                <flux:field variant="inline" class="py-3">
                    <flux:checkbox wire:model="is_export_invoice" />
                    <flux:label>Xuất hoá đơn điện tử</flux:label>
                </flux:field>
                <flux:field variant="inline" class="py-3">
                    <flux:checkbox wire:model="is_print_bill" />
                    <flux:label>In hoá đơn sau khi thanh toán</flux:label>
                </flux:field>
                <flux:button icon="shopping-cart" variant="primary" class="w-full !h-12 bg-blue-500! border-blue-500! text-white!" wire:click='checkout'>Thanh toán</flux:button>
            </div>
            <div class="col-span-1">
                <flux:heading size="lg">Mệnh giá</flux:heading>
                <!-- Mệnh giá gợi ý -->
                <div class="flex gap-3 flex-wrap mb-4 mt-6">
                    <template x-for="amount in suggestions">
                        <template x-if="amount">
                        <button
                            @click="paid = amount.toString(); triggerMask();"
                            class="px-4 py-2 bg-gray-100 hover:bg-blue-100 rounded  text-sm font-medium"
                            x-text="Intl.NumberFormat('vi-VN').format(amount) + 'đ'"
                        ></button>
                        </template>
                    </template>
                </div>

                <!-- Bàn phím số -->
                <div class="grid grid-cols-3 gap-2 mb-4">
                    <template x-for="n in [7,8,9,4,5,6,1,2,3]" :key="n">
                        <button @click="add(n)" class="bg-gray-100 hover:bg-gray-200 text-xl py-3 rounded shadow">
                            <span x-text="n"></span>
                        </button>
                    </template>
                    <button @click="clear" class="bg-yellow-100 hover:bg-yellow-200 text-xl py-3 rounded shadow">
                        <i class="ph ph-arrow-counter-clockwise"></i>
                    </button>
                    <button @click="add('0')" class="bg-gray-100 hover:bg-gray-200 text-xl py-3 rounded shadow">0</button>
                    <button @click="back" class="bg-red-100 hover:bg-red-200 text-xl py-3 rounded shadow">
                        <i class="ph ph-arrow-left"></i>
                    </button>
                </div>

            </div>
        </div>

    </flux:modal>
</div>

<x-shopper::container>
    <x-shopper::breadcrumb :back="route('shopper.settings.index')" :current="__('Kết nối hoá đơn điện tử')">
        <x-untitledui-chevron-left class="size-4 shrink-0 text-gray-300 dark:text-gray-600" aria-hidden="true" />
        <x-shopper::breadcrumb.link
            :link="route('shopper.settings.index')"
            :title="__('shopper::pages/settings/global.menu')"
        />
    </x-shopper::breadcrumb>

    <x-shopper::heading class="mt-6" :title="__('Kết nối hoá đơn điện tử')" />
    <div class="mt-6">
        <x-filament::section>
            <div class="flex gap-4 items-center">
                <flux:heading size="md">Đ<PERSON><PERSON> tác xuất hoá đơn</flux:heading>
                <x-filament::input.wrapper>
                <x-filament::input.select wire:model.live="providerCode">
                    <option value="">Chọn đối tác</option>
                    <option value="VNPT">VNPT</option>
                </x-filament::input.select>
            </x-filament::input.wrapper>
            </div>
        </x-filament::section>
    </div>
    @if($providerCode)
    <div class="mt-6 grid grid-cols-2 gap-4">
        <x-filament::section>
            <flux:heading size="md">Thông tin cấu hình để xuất hoá đơn cho: <span class="font-bold">{{ $providerCode }}</span></flux:heading>
            <div class="mt-6 flex flex-col gap-3">
                <div class="">
                    <flux:label>Link API <span class="text-danger-600">*</span></flux:label>
                   <x-filament::input.wrapper :valid="! $errors->has('invoiceIntegrationConfig.link_api')">
                        <x-filament::input
                            type="text"
                            wire:model="invoiceIntegrationConfig.link_api"
                            placeholder="Nhập link API"
                        />
                    </x-filament::input.wrapper>
                    <div class="flex justify-end">
                        <p class="text-sm text-gray-500">Ví dụ: https://xxxx.vnpt-invoice.com.vn</p>
                    </div>
                </div>
                <div class="">
                    <flux:label>Username <span class="text-danger-600">*</span></flux:label>
                    <x-filament::input.wrapper :valid="! $errors->has('invoiceIntegrationConfig.username')">
                        <x-filament::input
                            type="text"
                            wire:model="invoiceIntegrationConfig.username"
                            placeholder="Nhập Username của tài khoản service"
                        />
                    </x-filament::input.wrapper>
                </div>
                <div class="">
                    <flux:label>Username Pass <span class="text-danger-600">*</span></flux:label>
                    <x-filament::input.wrapper :valid="! $errors->has('invoiceIntegrationConfig.username_pass')">
                        <x-filament::input
                            type="password"
                            wire:model="invoiceIntegrationConfig.username_pass"
                            placeholder="Nhập mật khẩu của tài khoản service"
                        />
                    </x-filament::input.wrapper>
                </div>
                <div class="">
                    <flux:label>Account <span class="text-danger-600">*</span></flux:label>
                    <x-filament::input.wrapper :valid="! $errors->has('invoiceIntegrationConfig.account')">
                        <x-filament::input
                            type="text"
                            wire:model="invoiceIntegrationConfig.account"
                            placeholder="Nhập account"
                        />
                    </x-filament::input.wrapper>
                </div>
                <div class="">
                    <flux:label>Account Pass <span class="text-danger-600">*</span></flux:label>
                    <x-filament::input.wrapper :valid="! $errors->has('invoiceIntegrationConfig.account_pass')">
                        <x-filament::input
                            type="password"
                            wire:model="invoiceIntegrationConfig.account_pass"
                            placeholder="Nhập mật khẩu của account"
                        />
                    </x-filament::input.wrapper>
                </div>
                <div class="">
                    <flux:label>Mẫu số hóa đơn</flux:label>
                    <x-filament::input.wrapper :valid="! $errors->has('invoiceIntegrationConfig.pattern_cash_register')">
                        <x-filament::input
                            type="text"
                            wire:model="invoiceIntegrationConfig.pattern_cash_register"
                            placeholder="Nhập mẫu số hóa đơn của hoá đơn VAT"
                        />
                    </x-filament::input.wrapper>
                </div>
                <div class="">
                    <flux:label>Ký hiệu hóa đơn</flux:label>
                    <x-filament::input.wrapper :valid="! $errors->has('invoiceIntegrationConfig.serial_cash_register')">
                        <x-filament::input
                            type="text"
                            wire:model="invoiceIntegrationConfig.serial_cash_register"
                            placeholder="Nhập ký hiệu hóa đơn của hoá đơn VAT"
                        />
                    </x-filament::input.wrapper>
                </div>
            </div>
            <div class="mt-6">
                <x-filament::button color="primary" wire:click='save'>
                    Lưu
                </x-filament::button>
            </div>
        </x-filament::section>
        <x-filament::section>
            <flux:heading>Xuất hóa đơn từ máy tính tiền</flux:heading>
            <div class="grid grid-cols-1 mt-6">
                <div class="col-span-1">
                    <flux:label class="mb-2">Thời gian bán hàng</flux:label>
                    <x-filament::input.wrapper>
                        <x-filament::input.select wire:model='syncInvoiceInfo.date'>
                            <option value="today">Hôm nay</option>
                            <option value="custom">Lựa chọn khác</option>
                        </x-filament::input.select>
                    </x-filament::input.wrapper>
                    <div class="mt-6">
                        <label>
                            <x-filament::input.checkbox wire:model="isSelect" />

                            <span>
                                Chỉ đồng bộ các đơn hàng yêu cầu xuất HĐĐT từ máy tính tiền
                            </span>
                        </label>
                    </div>
                    <div class="mt-6">
                        <x-filament::button color="primary">
                            Đồng bộ phát hành hoá đơn
                        </x-filament::button>
                    </div>
                </div>
            </div>
        </x-filament::section>
    </div>
    @endif

    {{-- <form wire:submit="store" class="mt-10">
        {{ $this->form }}

        <div class="mt-10 border-t border-gray-200 pt-10 dark:border-white/10">
            <div class="flex justify-end">
                <x-shopper::buttons.primary type="submit" wire:loading.attr="disabled">
                    <x-shopper::loader wire:loading wire:target="store" class="text-white" />
                    {{ __('shopper::forms.actions.save') }}
                </x-shopper::buttons.primary>
            </div>
        </div>
    </form> --}}
</x-shopper::container>

<div>
    <form wire:submit="save">
        {{ $this->form }}
        
        <div class="mt-6 flex justify-end">
            <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                Save Tax Configuration
            </button>
        </div>
    </form>

    @if($product->hasTaxes())
        <div class="mt-8">
            <h4 class="text-lg font-medium text-gray-900 mb-4">Tax Preview</h4>
            <div class="bg-gray-50 rounded-lg p-4">
                <div class="space-y-2">
                    @php
                        $samplePrice = 10000; // $100.00 in cents
                        $taxCalculation = $product->calculateTaxAmount($samplePrice);
                    @endphp
                    
                    <div class="flex justify-between text-sm">
                        <span>Sample Price:</span>
                        <span class="font-medium">{{ shopper_money_format($samplePrice) }}</span>
                    </div>
                    
                    @foreach($taxCalculation['tax_breakdown'] as $tax)
                        <div class="flex justify-between text-sm">
                            <span>{{ $tax['tax_name'] }} ({{ format_tax_rate((float) $tax['tax_rate']) }}):</span>
                            <span>{{ shopper_money_format($tax['tax_amount']) }}</span>
                        </div>
                    @endforeach
                    
                    <div class="border-t pt-2 flex justify-between font-medium">
                        <span>Total Tax:</span>
                        <span>{{ shopper_money_format($taxCalculation['total_tax_amount']) }}</span>
                    </div>
                    
                    <div class="flex justify-between font-bold text-lg">
                        <span>Total with Tax:</span>
                        <span>{{ shopper_money_format($samplePrice + $taxCalculation['total_tax_amount']) }}</span>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>

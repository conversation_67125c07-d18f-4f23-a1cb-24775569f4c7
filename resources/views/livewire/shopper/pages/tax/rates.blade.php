<x-shopper::container class="py-5">
        <x-slot:title>
            {{ $tax->name }} - Tax Rates
        </x-slot:title>

        <div class="space-y-6">
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="sm:flex sm:items-center sm:justify-between">
                        <div>
                            <h3 class="text-lg leading-6 font-medium text-gray-900">
                                {{ $tax->name }} ({{ $tax->code }})
                            </h3>
                            <p class="mt-1 max-w-2xl text-sm text-gray-500">
                                {{ $tax->description ?: 'Manage tax rates for different zones and currencies.' }}
                            </p>
                            <div class="mt-2 flex items-center space-x-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $tax->type === 'percentage' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                    {{ ucfirst(str_replace('_', ' ', $tax->type)) }}
                                </span>
                                @if($tax->is_active)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Active
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        Inactive
                                    </span>
                                @endif
                                @if($tax->is_inclusive)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        Inclusive
                                    </span>
                                @endif
                                @if($tax->is_compound)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                        Compound (Priority: {{ $tax->priority }})
                                    </span>
                                @endif
                            </div>
                        </div>
                        <div class="mt-4 sm:mt-0">
                            <a href="{{ route('shopper.taxes.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                ← Back to Taxes
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="mb-4">
                        <h4 class="text-md font-medium text-gray-900">Tax Rates</h4>
                        <p class="mt-1 text-sm text-gray-500">
                            Configure different tax rates for different zones and currencies. Global rates apply when no zone-specific rate is found.
                        </p>
                    </div>
                    {{ $this->table }}
                </div>
            </div>
        </div>
</x-shopper::container>

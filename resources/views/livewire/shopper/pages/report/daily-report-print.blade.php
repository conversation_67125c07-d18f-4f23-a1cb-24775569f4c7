<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily Sales Report - {{ $selectedDate }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            background: white;
        }
        
        .container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        
        .header h1 {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .header .company-info {
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .header .report-info {
            font-size: 12px;
            color: #666;
        }
        
        .summary-section {
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            padding: 8px 0;
            border-bottom: 1px solid #ddd;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .summary-card {
            border: 1px solid #ddd;
            padding: 15px;
            text-align: center;
        }
        
        .summary-card .label {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .summary-card .value {
            font-size: 14px;
            font-weight: bold;
        }
        
        .staff-section {
            margin-bottom: 40px;
            page-break-inside: avoid;
        }
        
        .staff-header {
            background: #f5f5f5;
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #ddd;
        }
        
        .staff-name {
            font-size: 14px;
            font-weight: bold;
        }
        
        .staff-email {
            font-size: 11px;
            color: #666;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .metric-item {
            text-align: center;
            padding: 8px;
            border: 1px solid #eee;
        }
        
        .metric-label {
            font-size: 10px;
            color: #666;
            margin-bottom: 3px;
        }
        
        .metric-value {
            font-weight: bold;
            font-size: 11px;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .table th,
        .table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            font-size: 11px;
        }
        
        .table th {
            background: #f5f5f5;
            font-weight: bold;
        }
        
        .table td.number {
            text-align: right;
        }
        
        .table td.center {
            text-align: center;
        }
        
        .payment-methods {
            margin-bottom: 20px;
        }
        
        .payment-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
        }
        
        .payment-item {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: center;
        }
        
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            font-size: 10px;
            color: #666;
        }
        
        @media print {
            body {
                font-size: 11px;
            }
            
            .container {
                padding: 10px;
            }
            
            .staff-section {
                page-break-inside: avoid;
            }
            
            .no-print {
                display: none;
            }
        }
        
        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            z-index: 1000;
        }
        
        .print-button:hover {
            background: #005a87;
        }
    </style>
</head>
<body>
    <button class="print-button no-print" onclick="window.print()">🖨️ Print Report</button>
    
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>Daily Sales Report</h1>
            <div class="company-info">
                <strong>{{ shopper_setting('name') ?? 'Store Name' }}</strong><br>
                {{ shopper_setting('address') ?? 'Store Address' }}
            </div>
            <div class="report-info">
                <strong>Report Date:</strong> {{ \Carbon\Carbon::parse($selectedDate)->format('l, F j, Y') }}<br>
                <strong>Generated:</strong> {{ $generatedAt }} by {{ $generatedBy }}
            </div>
        </div>

        @if(isset($error))
            <!-- Error Message -->
            <div class="text-center py-20">
                <div class="text-red-600 text-lg font-medium">{{ $error }}</div>
                <p class="text-gray-500 mt-2">Please check the staff ID and try again.</p>
            </div>
        @elseif(isset($summaryOnly) && $summaryOnly)
            <!-- Summary Only View -->
            <div class="summary-section">
                <div class="section-title">Daily Summary</div>
                <div class="summary-grid">
                    <div class="summary-card">
                        <div class="label">Total Orders</div>
                        <div class="value">{{ $totalSummary['total_orders'] ?? 0 }}</div>
                    </div>
                    <div class="summary-card">
                        <div class="label">Total Sales</div>
                        <div class="value">{{ shopper_money_format($totalSummary['total_sales'] ?? 0) }}</div>
                    </div>
                    <div class="summary-card">
                        <div class="label">Total Discount</div>
                        <div class="value">{{ shopper_money_format($totalSummary['total_discount'] ?? 0) }}</div>
                    </div>
                    <div class="summary-card">
                        <div class="label">Total VAT</div>
                        <div class="value">{{ shopper_money_format($totalSummary['total_vat'] ?? 0) }}</div>
                    </div>
                    <div class="summary-card">
                        <div class="label">Total Refunds</div>
                        <div class="value">{{ shopper_money_format($totalSummary['total_refunds'] ?? 0) }}</div>
                    </div>
                    <div class="summary-card">
                        <div class="label">Cancelled Orders</div>
                        <div class="value">{{ $totalSummary['total_cancelled'] ?? 0 }}</div>
                    </div>
                </div>
                
                <!-- Staff Summary Table -->
                <table class="table">
                    <thead>
                        <tr>
                            <th>Staff Name</th>
                            <th>Orders</th>
                            <th>Sales</th>
                            <th>Discount</th>
                            <th>VAT</th>
                            <th>Refunds</th>
                            <th>Cancelled</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($reportData as $staff)
                            <tr>
                                <td>{{ $staff['staff_name'] }}</td>
                                <td class="center">{{ $staff['number_of_orders'] }}</td>
                                <td class="number">{{ shopper_money_format($staff['total_sales']) }}</td>
                                <td class="number">{{ shopper_money_format($staff['total_discount']) }}</td>
                                <td class="number">{{ shopper_money_format($staff['vat_amount']) }}</td>
                                <td class="number">{{ shopper_money_format($staff['refund_amount']) }}</td>
                                <td class="center">{{ $staff['cancelled_orders'] }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <!-- Full Report View -->
            <div class="summary-section">
                <div class="section-title">Daily Summary</div>
                <div class="summary-grid">
                    <div class="summary-card">
                        <div class="label">Total Orders</div>
                        <div class="value">{{ $totalSummary['total_orders'] ?? 0 }}</div>
                    </div>
                    <div class="summary-card">
                        <div class="label">Total Sales</div>
                        <div class="value">{{ shopper_money_format($totalSummary['total_sales'] ?? 0) }}</div>
                    </div>
                    <div class="summary-card">
                        <div class="label">Total Discount</div>
                        <div class="value">{{ shopper_money_format($totalSummary['total_discount'] ?? 0) }}</div>
                    </div>
                    <div class="summary-card">
                        <div class="label">Total VAT</div>
                        <div class="value">{{ shopper_money_format($totalSummary['total_vat'] ?? 0) }}</div>
                    </div>
                    <div class="summary-card">
                        <div class="label">Total Refunds</div>
                        <div class="value">{{ shopper_money_format($totalSummary['total_refunds'] ?? 0) }}</div>
                    </div>
                    <div class="summary-card">
                        <div class="label">Cancelled Orders</div>
                        <div class="value">{{ $totalSummary['total_cancelled'] ?? 0 }}</div>
                    </div>
                </div>
            </div>

            <!-- Staff Details -->
            @foreach($reportData as $staff)
                <div class="staff-section">
                    <div class="staff-header">
                        <div class="staff-name">{{ $staff['staff_name'] }}</div>
                        <div class="staff-email">{{ $staff['staff_email'] }}</div>
                    </div>

                    <div class="metrics-grid">
                        <div class="metric-item">
                            <div class="metric-label">Total Orders</div>
                            <div class="metric-value">{{ $staff['number_of_orders'] }}</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-label">Completed</div>
                            <div class="metric-value">{{ $staff['completed_orders'] }}</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-label">Cancelled</div>
                            <div class="metric-value">{{ $staff['cancelled_orders'] }}</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-label">Total Sales</div>
                            <div class="metric-value">{{ shopper_money_format($staff['total_sales']) }}</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-label">Discount</div>
                            <div class="metric-value">{{ shopper_money_format($staff['total_discount']) }}</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-label">VAT</div>
                            <div class="metric-value">{{ shopper_money_format($staff['vat_amount']) }}</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-label">Net Sales</div>
                            <div class="metric-value">{{ shopper_money_format($staff['net_sales']) }}</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-label">Refunds</div>
                            <div class="metric-value">{{ shopper_money_format($staff['refund_amount']) }}</div>
                        </div>
                    </div>

                    @if(count($staff['payment_methods']) > 0)
                        <div class="section-title">Payment Methods</div>
                        <div class="payment-grid">
                            @foreach($staff['payment_methods'] as $method)
                                <div class="payment-item">
                                    <div style="font-weight: bold;">{{ $method['name'] }}</div>
                                    <div>{{ $method['count'] }} orders</div>
                                    <div>{{ shopper_money_format($method['total_amount']) }}</div>
                                </div>
                            @endforeach
                        </div>
                    @endif

                    @if(count($staff['product_sales']) > 0)
                        <div class="section-title">Top Products</div>
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Product Name</th>
                                    <th>SKU</th>
                                    <th>Qty Sold</th>
                                    <th>Unit Price</th>
                                    <th>Total Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach(array_slice($staff['product_sales'], 0, 10) as $product)
                                    <tr>
                                        <td>{{ $product['product_name'] }}</td>
                                        <td>{{ $product['sku'] }}</td>
                                        <td class="center">{{ $product['quantity_sold'] }}</td>
                                        <td class="number">{{ shopper_money_format($product['unit_price']) }}</td>
                                        <td class="number">{{ shopper_money_format($product['total_amount']) }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    @endif
                </div>
            @endforeach
        @endif

        <!-- Footer -->
        <div class="footer">
            <p>This report was generated automatically by the POS system.</p>
            <p>Generated on {{ $generatedAt }} by {{ $generatedBy }}</p>
            <p>© {{ date('Y') }} {{ shopper_setting('name') ?? 'Store Name' }}. All rights reserved.</p>
        </div>
    </div>

    <script>
        // Auto-print when opened in new window
        if (window.location.search.includes('auto-print=1')) {
            window.onload = function() {
                setTimeout(function() {
                    window.print();
                }, 500);
            };
        }

        // Print function
        function printReport() {
            window.print();
        }

        // Keyboard shortcut for printing
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
        });
    </script>
</body>
</html>

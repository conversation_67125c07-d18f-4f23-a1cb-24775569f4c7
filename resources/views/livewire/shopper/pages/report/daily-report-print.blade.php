<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tổng kết cuối ng<PERSON>y - {{ now()->startOfDay()->format('d/m/Y H:i:s') }} - {{ now()->format('d/m/Y H:i') }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #000;
            background: white;
            padding: 20px;
        }

        .container {
            max-width: 80mm;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
        }

        .header h1 {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            text-transform: uppercase;
        }

        .date-range {
            font-size: 14px;
            margin-bottom: 20px;
        }

        .main-table {
            width: 100%;
            border-collapse: collapse;
            border: 2px solid #000;
            margin-bottom: 20px;
        }

        .main-table th {
            background-color: #e6e6e6;
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
            font-weight: bold;
            font-size: 12px;
        }

        .main-table td {
            border: 1px solid #000;
            padding: 6px 8px;
            font-size: 11px;
        }

        .main-table td.number {
            text-align: right;
            font-weight: normal;
        }

        .main-table td.center {
            text-align: center;
        }

        .row-number {
            text-align: center;
            font-weight: bold;
            width: 30px;
        }

        .description {
            font-weight: bold;
        }

        .sub-item {
            padding-left: 15px;
            font-style: italic;
        }

        .product-row {
            background-color: #f9f9f9;
        }

        .total-row {
            font-weight: bold;
            background-color: #f0f0f0;
        }

        .staff-section {
            page-break-inside: avoid;
            margin-bottom: 40px;
        }

        .staff-header {
            background-color: #f0f0f0;
            border: 1px solid #000;
            padding: 10px;
            text-align: center;
            margin-bottom: 15px;
            font-weight: bold;
            font-size: 14px;
        }

        .footer {
            text-align: center;
            margin-top: 30px;
            font-size: 11px;
        }

        .footer .timestamp {
            margin-bottom: 5px;
        }

        .footer .powered-by {
            font-weight: bold;
        }

        @media print {
            body {
                padding: 10px;
            }

            .no-print {
                display: none !important;
            }

            .action-buttons {
                display: none !important;
            }

            .print-button {
                display: none !important;
            }

            .back-button {
                display: none !important;
            }

            .staff-section {
                page-break-inside: avoid;
            }
        }

        .action-buttons {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            display: flex;
            gap: 10px;
        }

        .print-button, .back-button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
        }

        .print-button:hover, .back-button:hover {
            background: #005a87;
        }

        .back-button {
            background: #6b7280;
        }

        .back-button:hover {
            background: #4b5563;
        }
    </style>
</head>
<body>
    <div class="action-buttons no-print">
        <a href="{{ route('shopper.report.daily-report') }}?date={{ $selectedDate }}" class="back-button">
            ← Quay lại báo cáo
        </a>
        <button class="print-button" onclick="window.print()">
            🖨️ In báo cáo
        </button>
    </div>

    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>TỔNG KẾT CUỐI NGÀY</h1>
            <div class="date-range">
                {{ now()->startOfDay()->format('d/m/Y H:i:s') }} - {{ now()->format('d/m/Y H:i') }}
            </div>
        </div>

        @if(isset($error))
            <!-- Error Message -->
            <div style="text-align: center; padding: 50px 0;">
                <div style="color: #dc2626; font-size: 16px; font-weight: bold;">{{ $error }}</div>
                <p style="color: #6b7280; margin-top: 10px;">Vui lòng kiểm tra mã nhân viên và thử lại.</p>
            </div>
        @else
            <!-- Individual Staff Report Tables -->
            @foreach($reportData as $staffIndex => $staff)
                @if($staffIndex > 0)
                    <div style="page-break-before: always;"></div>
                    <!-- Header for new page -->
                    <div class="header">
                        <h1>TỔNG KẾT CUỐI NGÀY</h1>
                        <div class="date-range">
                            {{ now()->startOfDay()->format('d/m/Y H:i:s') }} - {{ now()->format('d/m/Y H:i') }}
                        </div>
                    </div>
                @endif

                <!-- Staff Name Header -->
                <div style="margin-bottom: 15px; padding: 10px; background-color: #f0f0f0; border: 1px solid #000; text-align: center;">
                    <strong style="font-size: 14px;">Nhân viên: {{ $staff['staff_name'] }}</strong>
                </div>

                <!-- Staff Report Table -->
                <table class="main-table">
                    <thead>
                        <tr>
                            <th style="width: 30px;"></th>
                            <th style="width: 50%; text-align: left;">
                                Tổng kết<br>
                            </th>
                            <th style="width: 15%; text-align: center;"></th>
                            <th style="width: 35%; text-align: center;">
                                Số tiền<br>
                                <em style="font-style: italic; font-weight: normal;">Amount</em>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Row 1: Number of orders -->
                        <tr>
                            <td class="row-number">1</td>
                            <td class="description">
                                Số đơn hàng<br>
                                <em style="font-style: italic; color: #666;">Number of orders</em>
                            </td>
                            <td></td>
                            <td class="number">{{ $staff['number_of_orders'] ?? 0 }}</td>
                        </tr>

                        <!-- Row 2: Discounts -->
                        <tr>
                            <td class="row-number">2</td>
                            <td class="description">
                                Chiết khấu<br>
                                <em style="font-style: italic; color: #666;">Total discount</em>
                            </td>
                            <td></td>
                            <td class="number">{{ shopper_money_format($staff['total_discount'] ?? 0) }}</td>
                        </tr>

                        <!-- Sub-rows for discounts -->
                        <tr>
                            <td></td>
                            <td class="sub-item">
                                Khác/Other
                            </td>
                            <td></td>
                            <td class="number">0</td>
                        </tr>
                        <tr>
                            <td></td>
                            <td class="sub-item">Voucher</td>
                            <td></td>
                            <td class="number">0</td>
                        </tr>

                        <!-- Row 3: Total sales -->
                        <tr>
                            <td class="row-number">3</td>
                            <td class="description">
                                Tổng doanh thu<br>
                                <em style="font-style: italic; color: #666;">Total sales</em>
                            </td>
                            <td></td>
                            <td class="number">{{ shopper_money_format($staff['total_sales']) }}</td>
                        </tr>

                        <!-- Row 4: VAT -->
                        <tr>
                            <td class="row-number">4</td>
                            <td class="description">VAT</td>
                            <td></td>
                            <td class="number">{{ shopper_money_format($staff['vat_amount'] ?? 0) }}</td>
                        </tr>

                        <!-- Row 5: Net sales -->
                        <tr>
                            <td class="row-number">5</td>
                            <td class="description">
                                Tổng doanh thu ròng<br>
                                <em style="font-style: italic; color: #666;">Net sales</em>
                            </td>
                            <td></td>
                            <td class="number">{{ shopper_money_format($staff['net_sales'] ?? 0) }}</td>
                        </tr>

                        <!-- Row 6: Refunds -->
                        <tr>
                            <td class="row-number">6</td>
                            <td class="description">Trả hàng/Refund</td>
                            <td></td>
                            <td class="number">{{ shopper_money_format($staff['refund_amount'] ?? 0) }}</td>
                        </tr>
                        <tr>
                            <td></td>
                            <td class="sub-item">
                                Doanh thu trừ trả hàng<br>
                                <em style="font-style: italic; color: #666;">Total after refund</em>
                            </td>
                            <td></td>
                            <td class="number">{{ shopper_money_format(($staff['total_sales'] ?? 0) - ($staff['refund_amount'] ?? 0)) }}</td>
                        </tr>

                        <!-- Row 7: Cancelled invoices -->
                        <tr>
                            <td class="row-number">7</td>
                            <td class="description">
                                Hóa đơn hủy<br>
                                <em style="font-style: italic; color: #666;">Invoice canceled</em>
                            </td>
                            <td></td>
                            <td class="number">{{ $staff['cancelled_orders'] ?? 0 }}</td>
                        </tr>

                        <!-- Row 8: Payment methods -->
                        <tr>
                            <td class="row-number">8</td>
                            <td class="description">
                                Phương thức thanh toán<br>
                                <em style="font-style: italic; color: #666;">Payment method</em>
                            </td>
                            <td></td>
                            <td class="number"></td>
                        </tr>

                        @foreach($staff['payment_methods'] as $method)
                            <tr>
                                <td></td>
                                <td class="sub-item">{{ $method['name'] }}</td>
                                <td></td>
                                <td class="number">{{ shopper_money_format($method['total_amount'] ?? 0) }}</td>
                            </tr>
                        @endforeach

                        <!-- Row 9: Total revenue -->
                        <tr>
                            <td class="row-number">9</td>
                            <td class="description">Thực thu</td>
                            <td></td>
                            <td class="number">{{ shopper_money_format($staff['total_sales'] ?? 0) }}</td>
                        </tr>

                        <!-- Row 10: Product sales -->
                        <tr>
                            <td class="row-number">10</td>
                            <td class="description">
                                Hàng hóa bán ra<br>
                                <em style="font-style: italic; color: #666;">Product sales</em>
                            </td>
                            <td></td>
                            <td class="number"></td>
                        </tr>

                        @php
                            $staffTotalQuantity = 0;
                            $staffTotalAmount = 0;
                        @endphp

                        @foreach($staff['product_sales'] as $product)
                            @php
                                $staffTotalQuantity += $product['quantity_sold'];
                                $staffTotalAmount += $product['total_amount'];
                            @endphp
                            <tr class="product-row">
                                <td></td>
                                <td class="sub-item">{{ strtoupper($product['product_name']) }}</td>
                                <td class="center">{{ $product['quantity_sold'] }}</td>
                                <td class="number">{{ shopper_money_format($product['total_amount']) }}</td>
                            </tr>
                        @endforeach

                        <!-- Total products row -->
                        <tr class="total-row">
                            <td></td>
                            <td class="sub-item"></td>
                            <td class="center"><strong>{{ $staffTotalQuantity }}</strong></td>
                            <td class="number"><strong>{{ shopper_money_format($staffTotalAmount) }}</strong></td>
                        </tr>

                        <!-- Row 11: Product discounts -->
                        <tr>
                            <td class="row-number">11</td>
                            <td class="description">
                                Giảm giá trên mặt hàng<br>
                                <em style="font-style: italic; color: #666;">Discount on products</em>
                            </td>
                            <td></td>
                            <td class="number"></td>
                        </tr>
                        <tr>
                            <td></td>
                            <td class="sub-item"></td>
                            <td></td>
                            <td class="number">0</td>
                        </tr>
                    </tbody>
                </table>

                @if($loop->last)
                    <!-- Footer -->
                    <div class="footer">
                        <div class="timestamp">{{ now()->format('d/m/Y H:i') }}</div>
                        <div class="powered-by">Powered by TinoPOS</div>
                    </div>
                @endif
            @endforeach
        @endif
    </div>

    <script>
        // Print function
        function printReport() {
            window.print();
        }

        // Keyboard shortcut for printing
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
        });
    </script>
</body>
</html>

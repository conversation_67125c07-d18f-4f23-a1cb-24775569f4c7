<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tổng kết cuối ng<PERSON>y - {{ $selectedDate }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #000;
            background: white;
            padding: 20px;
        }

        .container {
            max-width: 210mm;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
        }

        .header h1 {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            text-transform: uppercase;
        }

        .date-range {
            font-size: 14px;
            margin-bottom: 20px;
        }

        .main-table {
            width: 100%;
            border-collapse: collapse;
            border: 2px solid #000;
            margin-bottom: 20px;
        }

        .main-table th {
            background-color: #e6e6e6;
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
            font-weight: bold;
            font-size: 12px;
        }

        .main-table td {
            border: 1px solid #000;
            padding: 6px 8px;
            font-size: 11px;
        }

        .main-table td.number {
            text-align: right;
            font-weight: normal;
        }

        .main-table td.center {
            text-align: center;
        }

        .row-number {
            text-align: center;
            font-weight: bold;
            width: 30px;
        }

        .description {
            font-weight: bold;
        }

        .sub-item {
            padding-left: 15px;
            font-style: italic;
        }

        .product-row {
            background-color: #f9f9f9;
        }

        .total-row {
            font-weight: bold;
            background-color: #f0f0f0;
        }

        .footer {
            text-align: center;
            margin-top: 30px;
            font-size: 11px;
        }

        .footer .timestamp {
            margin-bottom: 5px;
        }

        .footer .powered-by {
            font-weight: bold;
        }

        @media print {
            body {
                padding: 10px;
            }

            .no-print {
                display: none;
            }
        }

        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            z-index: 1000;
        }

        .print-button:hover {
            background: #005a87;
        }
    </style>
</head>
<body>
    <button class="print-button no-print" onclick="window.print()">🖨️ In báo cáo</button>

    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>TỔNG KẾT CUỐI NGÀY</h1>
            <div class="date-range">
                {{ \Carbon\Carbon::parse($selectedDate)->format('d/m/Y') }} 00:00 - {{ \Carbon\Carbon::parse($selectedDate)->format('d/m/Y H:i') }}
            </div>
        </div>

        @if(isset($error))
            <!-- Error Message -->
            <div style="text-align: center; padding: 50px 0;">
                <div style="color: #dc2626; font-size: 16px; font-weight: bold;">{{ $error }}</div>
                <p style="color: #6b7280; margin-top: 10px;">Vui lòng kiểm tra mã nhân viên và thử lại.</p>
            </div>
        @else
            <!-- Main Report Table -->
            <table class="main-table">
                <thead>
                    <tr>
                        <th style="width: 30px;"></th>
                        <th style="width: 50%; text-align: left;">
                            Tổng kết<br>
                            <em style="font-style: italic; font-weight: normal;">Thu Ngân</em>
                        </th>
                        <th style="width: 15%; text-align: center;"></th>
                        <th style="width: 35%; text-align: center;">
                            Số tiền<br>
                            <em style="font-style: italic; font-weight: normal;">Amount</em>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Row 1: Number of orders -->
                    <tr>
                        <td class="row-number">1</td>
                        <td class="description">
                            Số đơn hàng<br>
                            <em style="font-style: italic; color: #666;">Number of orders</em>
                        </td>
                        <td></td>
                        <td class="number">{{ $totalSummary['total_orders'] ?? 0 }}</td>
                    </tr>

                    <!-- Row 2: Discounts -->
                    <tr>
                        <td class="row-number">2</td>
                        <td class="description">
                            Chiết khấu<br>
                            <em style="font-style: italic; color: #666;">Total discount</em>
                        </td>
                        <td></td>
                        <td class="number">{{ number_format(($totalSummary['total_discount'] ?? 0) / 100, 0, ',', '.') }}</td>
                    </tr>

                    <!-- Sub-rows for discounts -->
                    <tr>
                        <td></td>
                        <td class="sub-item">
                            Khác/Other
                        </td>
                        <td></td>
                        <td class="number">0</td>
                    </tr>
                    <tr>
                        <td></td>
                        <td class="sub-item">Voucher</td>
                        <td></td>
                        <td class="number">0</td>
                    </tr>

                    <!-- Row 3: Total sales -->
                    <tr>
                        <td class="row-number">3</td>
                        <td class="description">
                            Tổng doanh thu<br>
                            <em style="font-style: italic; color: #666;">Total sales</em>
                        </td>
                        <td></td>
                        <td class="number">{{ number_format(($totalSummary['total_sales'] ?? 0) / 100, 0, ',', '.') }}</td>
                    </tr>

                    <!-- Row 4: VAT -->
                    <tr>
                        <td class="row-number">4</td>
                        <td class="description">VAT</td>
                        <td></td>
                        <td class="number">{{ number_format(($totalSummary['total_vat'] ?? 0) / 100, 0, ',', '.') }}</td>
                    </tr>

                    <!-- Row 5: Net sales -->
                    <tr>
                        <td class="row-number">5</td>
                        <td class="description">
                            Tổng doanh thu ròng<br>
                            <em style="font-style: italic; color: #666;">Net sales</em>
                        </td>
                        <td></td>
                        <td class="number">{{ number_format((($totalSummary['total_sales'] ?? 0) - ($totalSummary['total_vat'] ?? 0)) / 100, 0, ',', '.') }}</td>
                    </tr>

                    <!-- Row 6: Refunds -->
                    <tr>
                        <td class="row-number">6</td>
                        <td class="description">Trả hàng/Refund</td>
                        <td></td>
                        <td class="number">{{ number_format(($totalSummary['total_refunds'] ?? 0) / 100, 0, ',', '.') }}</td>
                    </tr>
                    <tr>
                        <td></td>
                        <td class="sub-item">
                            Doanh thu trừ trả hàng<br>
                            <em style="font-style: italic; color: #666;">Total after refund</em>
                        </td>
                        <td></td>
                        <td class="number">{{ number_format((($totalSummary['total_sales'] ?? 0) - ($totalSummary['total_refunds'] ?? 0)) / 100, 0, ',', '.') }}</td>
                    </tr>

                    <!-- Row 7: Cancelled invoices -->
                    <tr>
                        <td class="row-number">7</td>
                        <td class="description">
                            Hóa đơn hủy<br>
                            <em style="font-style: italic; color: #666;">Invoice canceled</em>
                        </td>
                        <td></td>
                        <td class="number">{{ $totalSummary['total_cancelled'] ?? 0 }}</td>
                    </tr>

                    <!-- Row 8: Payment methods -->
                    <tr>
                        <td class="row-number">8</td>
                        <td class="description">
                            Phương thức thanh toán<br>
                            <em style="font-style: italic; color: #666;">Payment method</em>
                        </td>
                        <td></td>
                        <td class="number"></td>
                    </tr>

                    @php
                        $paymentSummary = [];
                        foreach($reportData as $staff) {
                            foreach($staff['payment_methods'] as $method) {
                                if (!isset($paymentSummary[$method['name']])) {
                                    $paymentSummary[$method['name']] = 0;
                                }
                                $paymentSummary[$method['name']] += $method['total_amount'];
                            }
                        }
                    @endphp

                    <tr>
                        <td></td>
                        <td class="sub-item">Tiền mặt/Cash</td>
                        <td></td>
                        <td class="number">{{ number_format(($paymentSummary['Tiền mặt'] ?? $paymentSummary['Cash'] ?? 0) / 100, 0, ',', '.') }}</td>
                    </tr>
                    <tr>
                        <td></td>
                        <td class="sub-item">Ghi nợ/Debt</td>
                        <td></td>
                        <td class="number">{{ number_format(($paymentSummary['Chuyển khoản'] ?? $paymentSummary['Transfer'] ?? 0) / 100, 0, ',', '.') }}</td>
                    </tr>

                    <!-- Row 9: Total revenue -->
                    <tr>
                        <td class="row-number">9</td>
                        <td class="description">Thực thu</td>
                        <td></td>
                        <td class="number">{{ number_format(($totalSummary['total_sales'] ?? 0) / 100, 0, ',', '.') }}</td>
                    </tr>

                    <!-- Row 10: Product sales -->
                    <tr>
                        <td class="row-number">10</td>
                        <td class="description">
                            Hàng hóa bán ra<br>
                            <em style="font-style: italic; color: #666;">Product sales</em>
                        </td>
                        <td></td>
                        <td class="number"></td>
                    </tr>

                    @php
                        $productSummary = [];
                        foreach($reportData as $staff) {
                            foreach($staff['product_sales'] as $product) {
                                if (!isset($productSummary[$product['product_name']])) {
                                    $productSummary[$product['product_name']] = [
                                        'quantity' => 0,
                                        'amount' => 0
                                    ];
                                }
                                $productSummary[$product['product_name']]['quantity'] += $product['quantity_sold'];
                                $productSummary[$product['product_name']]['amount'] += $product['total_amount'];
                            }
                        }
                        $totalQuantity = array_sum(array_column($productSummary, 'quantity'));
                        $totalAmount = array_sum(array_column($productSummary, 'amount'));
                    @endphp

                    @foreach($productSummary as $productName => $data)
                        <tr class="product-row">
                            <td></td>
                            <td class="sub-item">{{ strtoupper($productName) }}</td>
                            <td class="center">{{ $data['quantity'] }}</td>
                            <td class="number">{{ number_format($data['amount'] / 100, 0, ',', '.') }}</td>
                        </tr>
                    @endforeach

                    <!-- Total products row -->
                    <tr class="total-row">
                        <td></td>
                        <td class="sub-item"></td>
                        <td class="center"><strong>{{ $totalQuantity }}</strong></td>
                        <td class="number"><strong>{{ number_format($totalAmount / 100, 0, ',', '.') }}</strong></td>
                    </tr>

                    <!-- Row 11: Product discounts -->
                    <tr>
                        <td class="row-number">11</td>
                        <td class="description">
                            Giảm giá trên mặt hàng<br>
                            <em style="font-style: italic; color: #666;">Discount on products</em>
                        </td>
                        <td></td>
                        <td class="number"></td>
                    </tr>
                    <tr>
                        <td></td>
                        <td class="sub-item"></td>
                        <td></td>
                        <td class="number">0</td>
                    </tr>
                </tbody>
            </table>

            <!-- Footer -->
            <div class="footer">
                <div class="timestamp">{{ \Carbon\Carbon::parse($selectedDate)->format('d/m/Y H:i') }}</div>
                <div class="powered-by">Powered by POS365.VN</div>
            </div>
        @endif
    </div>

    <script>
        // Auto-print when opened in new window
        if (window.location.search.includes('auto-print=1')) {
            window.onload = function() {
                setTimeout(function() {
                    window.print();
                }, 500);
            };
        }

        // Print function
        function printReport() {
            window.print();
        }

        // Keyboard shortcut for printing
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
        });
    </script>
</body>
</html>

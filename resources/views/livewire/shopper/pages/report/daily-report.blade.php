<x-shopper::container class="py-5">
    <div class="space-y-6">
        <!-- Date Selector -->
        <div class="bg-white rounded-lg shadow p-2">
            <div class="flex flex-col items-center justify-center">
                <h2 class="text-lg font-bold text-gray-900">TỔNG KẾT CUỐI NGÀY</h2>
                <p class="text-sm">{{ vietnamese_long_date(now()->startOfDay()) }} đến {{ vietnamese_long_date(now()) }}</p>
                {{-- <div class="flex items-center space-x-4">
                    <input type="date" wire:model.live="selectedDate"
                        class="rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                    <button wire:click="generateReport"
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Refresh Report
                    </button>
                </div> --}}
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Tổng đơn hàng</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $totalSummary['total_orders'] ?? 0 }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                            </svg>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Tổng doanh thu</dt>
                                <dd class="text-lg font-medium text-gray-900">{{
                                    shopper_money_format($totalSummary['total_sales'] ?? 0) }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                            </svg>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Tổng thuế</dt>
                                <dd class="text-lg font-medium text-gray-900">{{
                                    shopper_money_format($totalSummary['total_vat'] ?? 0) }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z" />
                            </svg>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Tổng trả hàng</dt>
                                <dd class="text-lg font-medium text-gray-900">{{
                                    shopper_money_format($totalSummary['total_refunds'] ?? 0) }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Staff Reports -->
        @if(count($reportData) > 0)
        <div class="bg-white shadow overflow-hidden rounded-md">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Thống kê cuối ngày của từng nhân viên</h3>
                <div class="flex items-center space-x-2">
                    <button
                        onclick="printFullReport()"
                        class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                        🖨️ In báo cáo đầy đủ
                    </button>
                </div>
            </div>
            <ul class="divide-y divide-gray-200">
                @foreach($reportData as $staff)
                <li class="divide-y divide-gray-200">
                    <div class="px-4 py-4 sm:px-6">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                        <span class="text-sm font-medium text-gray-700">{{ substr($staff['staff_name'],
                                            0, 2) }}</span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">Họ và tên: {{ $staff['staff_name'] }}</div>
                                    <div class="text-sm text-gray-500">Nhân viên bán hàng</div>
                                </div>
                            </div>
                            <div class="flex items-center gap-5">
                                <div class="text-center">
                                    <div class="text-sm font-medium text-gray-900">{{ $staff['number_of_orders'] }}
                                    </div>
                                    <div class="text-xs text-gray-500">Đơn hàng</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-sm font-medium text-gray-900">{{
                                        shopper_money_format($staff['total_sales']) }}</div>
                                    <div class="text-xs text-gray-500">Doanh thu</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-sm font-medium text-gray-900">{{
                                        shopper_money_format($staff['vat_amount']) }}</div>
                                    <div class="text-xs text-gray-500">Thuế</div>
                                </div>
                                <div class="text-center">
                                    <button
                                        onclick="printStaffReport({{ $staff['staff_id'] }})"
                                        class="inline-flex items-center px-2 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                    >
                                        🖨️ In riêng
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Detailed breakdown -->
                        <div class="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                                <span class="text-gray-500">Giảm giá:</span>
                                <span class="font-medium">{{ shopper_money_format($staff['total_discount']) }}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">Doanh thu thuần:</span>
                                <span class="font-medium">{{ shopper_money_format($staff['net_sales']) }}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">Trả hàng:</span>
                                <span class="font-medium">{{ shopper_money_format($staff['refund_amount']) }}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">Đơn hàng bị hủy:</span>
                                <span class="font-medium">{{ $staff['cancelled_orders'] }}</span>
                            </div>
                        </div>

                        <!-- Payment Methods -->
                        @if(count($staff['payment_methods']) > 0)
                        <div class="mt-4">
                            <h4 class="text-sm font-medium text-gray-900 mb-2">Phương thức thanh toán</h4>
                            <div class="grid grid-cols-2 md:grid-cols-3 gap-2 text-sm">
                                @foreach($staff['payment_methods'] as $method)
                                <div class="bg-gray-50 rounded p-2">
                                    <div class="font-medium">{{ $method['name'] }}</div>
                                    <div class="text-gray-500">{{ $method['count'] }} đơn hàng</div>
                                    <div class="text-gray-900">{{ shopper_money_format($method['total_amount']) }}</div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                        @endif

                        <!-- Top Products -->
                        @if(count($staff['product_sales']) > 0)
                        <div class="mt-4">
                            <h4 class="text-sm font-medium text-gray-900 mb-2">Danh sách sản phẩm</h4>
                            <div class="space-y-1 text-sm divide-y divide-gray-200 divide-dashed">
                                @foreach($staff['product_sales'] as $product)
                                <div class="flex justify-between">
                                    <span class="text-gray-600">{{ $product['product_name'] }}</span>
                                    <span class="font-medium">{{ $product['quantity_sold'] }} units - {{
                                        shopper_money_format($product['total_amount']) }}</span>
                                </div>
                                @endforeach
                            </div>
                        </div>
                        @endif
                    </div>
                </li>
                @endforeach
            </ul>
        </div>
        @else
        <div class="bg-white shadow sm:rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <div class="text-center">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">Không có dữ liệu</h3>
                    <p class="mt-1 text-sm text-gray-500">Chưa có hoá đơn nào trong ngày {{ $selectedDate }}.
                    </p>
                </div>
            </div>
        </div>
        @endif
    </div>
</x-shopper::container>

<script>
    // Print full report
    function printFullReport() {
        const url = '{{ route("shopper.report.daily.print") }}?date={{ $selectedDate }}';
        window.location.href = url;
    }

    // Print summary only
    function printSummaryOnly() {
        const url = '{{ route("shopper.report.daily.print-summary") }}?date={{ $selectedDate }}';
        window.location.href = url;
    }

    // Print individual staff report
    function printStaffReport(staffId) {
        const url = '{{ route("shopper.report.daily.print-staff") }}?date={{ $selectedDate }}&staff_id=' + staffId;
        window.location.href = url;
    }

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl+P for full report
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            printFullReport();
        }
        // Ctrl+Shift+P for summary
        if (e.ctrlKey && e.shiftKey && e.key === 'P') {
            e.preventDefault();
            printSummaryOnly();
        }
    });

    // Auto-refresh report every 5 minutes
    setInterval(function() {
        @this.generateReport();
    }, 300000); // 5 minutes
</script>
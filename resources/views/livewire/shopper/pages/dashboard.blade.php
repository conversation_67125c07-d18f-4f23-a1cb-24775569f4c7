<x-shopper::container class="py-5">
    <div class="flex items-center mb-6">
        <h2
            class="welcome font-heading text-3xl font-bold leading-8 text-gray-950 dark:text-white sm:truncate sm:leading-9"
        >
            Tổng quan
        </h2>
    </div>
    
    <!-- Summary Cards -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3 mt-5">
        <!-- Orders Summary -->
        <div class="overflow-hidden rounded-lg bg-white border border-gray-200">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-gray-400 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 10.5V6a3.75 3.75 0 10-7.5 0v4.5m11.356-1.993l1.263 12c.07.665-.45 1.243-1.119 1.243H4.25a1.125 1.125 0 01-1.12-1.243l1.264-12A1.125 1.125 0 015.513 7.5h12.974c.576 0 1.059.435 1.119 1.007zM8.625 10.5a.375.375 0 11-.75 0 .375.375 0 01.75 0zm7.5 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="truncate text-sm font-medium text-gray-500 dark:text-gray-400">Đơn hàng hoàn thành</dt>
                            <dd>
                                <div class="text-lg font-medium text-gray-900 dark:text-white">{{ $totalOrders }}</div>
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-5 py-2 dark:bg-gray-700">
                <div class="text-sm">
                    <a href="{{ route('shopper.orders.index') }}" class="font-medium text-primary-600 hover:text-primary-500 dark:text-primary-500 dark:hover:text-primary-400">
                        Xem tất cả
                    </a>
                </div>
            </div>
        </div>

        <!-- Customers Summary -->
        <div class="overflow-hidden rounded-lg bg-white border border-gray-200">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-gray-400 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z" />
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="truncate text-sm font-medium text-gray-500 dark:text-gray-400">Khách hàng</dt>
                            <dd>
                                <div class="text-lg font-medium text-gray-900 dark:text-white">{{ $totalCustomers }}</div>
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-5 py-2 dark:bg-gray-700">
                <div class="text-sm">
                    <a href="{{ route('shopper.customers.index') }}" class="font-medium text-primary-600 hover:text-primary-500 dark:text-primary-500 dark:hover:text-primary-400">
                        Xem tất cả
                    </a>
                </div>
            </div>
        </div>

        <!-- Products Summary -->
        <div class="overflow-hidden rounded-lg bg-white border border-gray-200">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-gray-400 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 7.5l-.625 10.632a2.25 2.25 0 01-2.247 2.118H6.622a2.25 2.25 0 01-2.247-2.118L3.75 7.5M10 11.25h4M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125z" />
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="truncate text-sm font-medium text-gray-500 dark:text-gray-400">Sản phẩm</dt>
                            <dd>
                                <div class="text-lg font-medium text-gray-900 dark:text-white">{{ $totalProducts }}</div>
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-5 py-2 dark:bg-gray-700">
                <div class="text-sm">
                    <a href="{{ route('shopper.products.index') }}" class="font-medium text-primary-600 hover:text-primary-500 dark:text-primary-500 dark:hover:text-primary-400">
                        Xem tất cả
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-2 mt-8 gap-5">
        <div class="col-span-1">
            @livewire(\App\Livewire\OrderChart::class)
        </div>
        <div class="col-span-1">
            @livewire(\App\Livewire\RevenueChart::class)
        </div>
    </div>
    <!-- Recent Orders -->
    <div class="mt-8">
        {{ $this->table }}
    </div>
    
</x-shopper::container>

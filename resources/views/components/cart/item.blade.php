@props([
    'cartItem',
])

@php
    $price = shopper_money_format(
        amount: $cartItem->price * $cartItem->quantity,
        currency: current_currency(),
    );

    $model = $cartItem->associatedModel;
    $originPrice = $model->getPrice();
@endphp

<div class="flex flex-col py-2" wire:key="{{ $cartItem->id }}">
<div class="flex gap-2 justify-between items-center" wire:key="{{ $cartItem->id }}">
    <div class="flex gap-2 items-center">
        <img src="{{ $model->getFirstMediaUrl(config('shopper.media.storage.thumbnail_collection')) }}" alt="product" class=" h-[40px] w-[40px] rounded-md border border-gray-300">
        <div class="flex flex-col">
            <p class="text-sm">{{ $model->name }}</p>
            @if ($model->sku)
            <p class="text-xs text-gray-500" >SKU: {{ $model->sku }}</p>
            @endif
        </div>
    </div>
    <div class="flex gap-3 items-end">
        <flux:button icon="trash" size="sm" class="border-red-300! text-red-500!" wire:click="removeToCart({{ $cartItem->id }})"></flux:button>
    </div>
</div>
<div class="flex gap-2 justify-between items-center" wire:key="{{ $cartItem->id }}">
    <div class="flex gap-4 items-center">
        <div class="flex flex-col">
            <div class="flex gap-2 items-center mt-2">
                <flux:input.group>
                    <flux:button size="sm" icon="minus" wire:click='decrementQuantity({{ $cartItem->id }})'></flux:button>
                    <flux:input 
                        value="{{ $cartItem->quantity }}" 
                        type="number" 
                        min="1" 
                        size="sm" 
                        class="w-[60px]! outline-none!" 
                        wire:model.blur="cartItemQuantity.{{ $cartItem->id }}"
                        wire:change="updateQuantity({{ $cartItem->id }}, $event.target.value)"
                    />
                    <flux:button size="sm" icon="plus" wire:click='incrementQuantity({{ $cartItem->id }})'></flux:button>
                </flux:input.group>
            </div>
        </div>
    </div>
    <div class="flex gap-3 items-end">
        <div class="flex flex-col">
            <p class="text-gray-600 text-sm mb-2">{{ $originPrice?->amount->formatted }}</p>
        </div>
        <div class="flex flex-col">
            <p class="text-blue-600 text-sm mb-2">= {{ $price }}</p>
        </div>
    </div>
</div>
</div>

@props([
    'product',
])

@php
    $price = $product->getPrice();
@endphp

<flux:card 
class="flex flex-col items-center text-center p-2 cursor-pointer relative" x-on:click="$wire.addToCart({{ $product->id }})">
    <img src="{{ $product->getFirstMediaUrl(config('shopper.media.storage.thumbnail_collection')) }}" alt="product" class="rounded-full w-[120px] h-[120px]">

    <span class="font-bold text-sm pt-2">{{ $product->name }}</span>
    <x-products.price :product="$product" />
    <x-badges.discount
        :discount="$price->percentage"
        class="ml-2 absolute top-2 right-2"
    />
    <flux:icon.loading class="absolute bottom-2 right-2" wire:loading wire:target="addToCart({{ $product->id }})" />
</flux:card>

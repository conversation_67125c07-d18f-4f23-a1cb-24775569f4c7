<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> cập bị hạn chế</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 60px 40px;
            text-align: center;
            max-width: 500px;
            width: 90%;
        }

        .error-icon {
            width: 120px;
            height: 120px;
            margin: 0 auto 30px;
            background: #e74c3c;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 60px;
            color: white;
        }

        h1 {
            font-size: 32px;
            color: #2c3e50;
            margin-bottom: 20px;
            font-weight: 700;
        }

        .error-code {
            font-size: 18px;
            color: #7f8c8d;
            margin-bottom: 30px;
            font-weight: 500;
        }

        .message {
            font-size: 16px;
            color: #555;
            line-height: 1.6;
            margin-bottom: 40px;
        }

        .reasons {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: left;
        }

        .reasons h3 {
            color: #856404;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .reasons ul {
            color: #856404;
            padding-left: 20px;
        }

        .reasons li {
            margin-bottom: 8px;
        }

        .actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
            transform: translateY(-2px);
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
            transform: translateY(-2px);
        }

        .contact-info {
            margin-top: 30px;
            padding: 20px;
            background: #e8f5e8;
            border-radius: 10px;
            font-size: 14px;
            color: #2d5a2d;
        }

        .details {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            font-size: 12px;
            color: #666;
        }

        @media (max-width: 480px) {
            .container {
                padding: 40px 20px;
            }
            
            h1 {
                font-size: 24px;
            }
            
            .error-icon {
                width: 80px;
                height: 80px;
                font-size: 40px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-icon">
            🚫
        </div>
        
        <h1>Truy cập bị hạn chế</h1>
        <div class="error-code">Lỗi 403 - Không có quyền truy cập</div>
        
        <div class="message">
            <p>Xin lỗi, tenant này hiện tại không thể truy cập vào trang web.</p>
        </div>

        <div class="reasons">
            <h3>Có thể do các lý do sau:</h3>
            <ul>
                <li>Tài khoản đã hết hạn sử dụng</li>
                <li>Gói dịch vụ đã bị tạm ngưng</li>
                <li>Tài khoản đang trong quá trình bảo trì</li>
                <li>Vi phạm điều khoản sử dụng</li>
                <li>Chưa thanh toán phí dịch vụ</li>
            </ul>
        </div>

        <div class="actions">
            <a href="javascript:history.back()" class="btn btn-secondary">← Quay lại</a>
            <a href="{{ config('app.url') }}" class="btn btn-primary">Trang chủ</a>
            <a href="mailto:<EMAIL>" class="btn btn-warning">Liên hệ hỗ trợ</a>
        </div>

        <div class="contact-info">
            <strong>Cần hỗ trợ?</strong><br>
            Email: <EMAIL><br>
            Hotline: 1900-xxxx<br>
            Thời gian hỗ trợ: 8:00 - 17:00 (T2-T6)
        </div>

        <div class="details">
            <strong>Thông tin kỹ thuật:</strong><br>
            Domain: {{ request()->getHost() }}<br>
            Tenant ID: {{ tenant('id') ?? 'N/A' }}<br>
            Thời gian: {{ now()->format('d/m/Y H:i:s') }}
        </div>
    </div>
</body>
</html>

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>C<PERSON><PERSON> hàng không ho<PERSON>t động</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 60px 40px;
            text-align: center;
            max-width: 600px;
            width: 90%;
        }

        .error-icon {
            width: 120px;
            height: 120px;
            margin: 0 auto 30px;
            background: #ff6b6b;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 60px;
            color: white;
        }

        h1 {
            font-size: 32px;
            color: #2c3e50;
            margin-bottom: 20px;
            font-weight: 700;
        }

        .error-code {
            font-size: 18px;
            color: #7f8c8d;
            margin-bottom: 30px;
            font-weight: 500;
        }

        .message {
            font-size: 16px;
            color: #555;
            line-height: 1.6;
            margin-bottom: 40px;
        }

        .actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
            transform: translateY(-2px);
        }

        .details {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            font-size: 14px;
            color: #666;
        }

        @media (max-width: 480px) {
            .container {
                padding: 40px 20px;
            }
            
            h1 {
                font-size: 24px;
            }
            
            .error-icon {
                width: 80px;
                height: 80px;
                font-size: 40px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Cửa hàng không hoạt động</h1>
        
        <div class="message">
            <p>Xin lỗi, cửa hàng mà bạn đang tìm kiếm không tồn tại hoặc đã bị vô hiệu hóa.</p>
            <p>Vui lòng kiểm tra lại địa chỉ URL hoặc liên hệ với quản trị viên.</p>
        </div>

        <div class="actions">
            <a href="javascript:history.back()" class="btn btn-secondary">← Quay lại</a>
            <a href="{{ config('app.url') }}" class="btn btn-primary">Trang chủ</a>
        </div>

        <div class="details">
            <strong>Thông tin kỹ thuật:</strong><br>
            Domain: {{ request()->getHost() }}<br>
            Thời gian: {{ now()->format('d/m/Y H:i:s') }}
        </div>
    </div>
</body>
</html>

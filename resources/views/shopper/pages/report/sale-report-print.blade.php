<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> c<PERSON>o bán hàng - {{ $periods[$period] ?? 'Tùy chỉnh' }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Times New Roman', serif;
            font-size: 12px;
            line-height: 1.4;
            color: #000;
            background: #fff;
            padding: 20px;
        }

        .container {
            max-width: 210mm;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #000;
            padding-bottom: 15px;
        }

        .header h1 {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
            text-transform: uppercase;
        }

        .header .period {
            font-size: 14px;
            margin-bottom: 5px;
        }

        .header .date-range {
            font-size: 12px;
            color: #666;
        }

        .action-buttons {
            text-align: center;
            margin-bottom: 20px;
        }

        .action-buttons button {
            background: #4F46E5;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }

        .action-buttons button:hover {
            background: #3730A3;
        }

        .summary-section {
            margin-bottom: 30px;
        }

        .summary-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 15px;
            text-transform: uppercase;
            border-bottom: 1px solid #ccc;
            padding-bottom: 5px;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }

        .summary-item {
            text-align: center;
            padding: 10px;
            border: 1px solid #ddd;
        }

        .summary-item .label {
            font-size: 10px;
            color: #666;
            margin-bottom: 5px;
        }

        .summary-item .value {
            font-size: 14px;
            font-weight: bold;
        }

        .day-section {
            margin-bottom: 40px;
            page-break-inside: avoid;
        }

        .day-header {
            background: #f5f5f5;
            padding: 10px;
            border: 1px solid #ddd;
            margin-bottom: 15px;
        }

        .day-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .day-summary {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            font-size: 11px;
        }

        .orders-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .orders-table th,
        .orders-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            font-size: 11px;
        }

        .orders-table th {
            background: #f5f5f5;
            font-weight: bold;
            text-align: center;
        }

        .orders-table .number {
            text-align: right;
        }

        .orders-table .center {
            text-align: center;
        }

        .total-row {
            background: #f9f9f9;
            font-weight: bold;
        }

        .page-break {
            page-break-before: always;
        }

        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }

        @media print {
            body {
                padding: 10px;
            }

            .action-buttons {
                display: none !important;
            }

            .day-section {
                page-break-inside: avoid;
            }

            .orders-table {
                page-break-inside: avoid;
            }

            .no-print {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <!-- Action Buttons -->
    <div class="action-buttons">
        <a href="{{ route('shopper.report.sale-report') }}" style="background: #6B7280; color: white; text-decoration: none; padding: 10px 20px; border-radius: 5px; margin: 0 10px;">
            ← Quay lại báo cáo
        </a>
        <button onclick="window.print()">
            🖨️ In báo cáo
        </button>
    </div>

    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>BÁO CÁO BÁN HÀNG</h1>
            <div class="period">{{ $periods[$period] ?? 'Tùy chỉnh' }}</div>
            <div class="date-range">
                Từ {{ vietnamese_long_date($startDate) }} đến {{ vietnamese_long_date($endDate) }}
            </div>
        </div>

        <!-- Summary Section -->
        <div class="summary-section">
            <div class="summary-title">Tổng quan</div>
            <div class="summary-grid">
                <div class="summary-item">
                    <div class="label">Tổng đơn hàng</div>
                    <div class="value">{{ number_format($totalSummary['total_orders']) }}</div>
                </div>
                <div class="summary-item">
                    <div class="label">Tổng doanh thu</div>
                    <div class="value">{{ shopper_money_format($totalSummary['total_sales']) }}</div>
                </div>
                <div class="summary-item">
                    <div class="label">Tổng giảm giá</div>
                    <div class="value">{{ shopper_money_format($totalSummary['total_discount']) }}</div>
                </div>
                <div class="summary-item">
                    <div class="label">Giá trị đơn hàng TB</div>
                    <div class="value">{{ shopper_money_format($totalSummary['average_order_value']) }}</div>
                </div>
            </div>
        </div>

        <!-- Daily Reports -->
        @if(count($reportData) > 0)
            @foreach($reportData as $dayData)
                <div class="day-section">
                    <!-- Day Header -->
                    <div class="day-header">
                        <div class="day-title">{{ vietnamese_formatted_date($dayData['date']) }}</div>
                        <div class="day-summary">
                            <div><strong>Đơn hàng:</strong> {{ $dayData['orders_count'] }}</div>
                            <div><strong>Doanh thu:</strong> {{ shopper_money_format($dayData['total_sales']) }}</div>
                            <div><strong>Giảm giá:</strong> {{ shopper_money_format($dayData['total_discount']) }}</div>
                            <div><strong>Doanh thu ròng:</strong> {{ shopper_money_format($dayData['net_sales']) }}</div>
                        </div>
                    </div>

                    <!-- Orders Table -->
                    @if(count($dayData['orders']) > 0)
                        <table class="orders-table">
                            <thead>
                                <tr>
                                    <th>STT</th>
                                    <th>Mã đơn hàng</th>
                                    <th>Thời gian</th>
                                    <th>Số SP</th>
                                    <th>Phương thức TT</th>
                                    <th>Giảm giá</th>
                                    <th>Tổng tiền</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($dayData['orders'] as $index => $order)
                                    <tr>
                                        <td class="center">{{ $index + 1 }}</td>
                                        <td>{{ $order['number'] }}</td>
                                        <td class="center">{{ $order['created_at']->format('H:i:s') }}</td>
                                        <td class="center">{{ $order['items_count'] }}</td>
                                        <td>{{ $order['payment_method'] }}</td>
                                        <td class="number">{{ shopper_money_format($order['discount']) }}</td>
                                        <td class="number">{{ shopper_money_format($order['total']) }}</td>
                                    </tr>
                                @endforeach
                                <!-- Day Total Row -->
                                <tr class="total-row">
                                    <td colspan="5" class="center"><strong>TỔNG NGÀY</strong></td>
                                    <td class="number"><strong>{{ shopper_money_format($dayData['total_discount']) }}</strong></td>
                                    <td class="number"><strong>{{ shopper_money_format($dayData['total_sales']) }}</strong></td>
                                </tr>
                            </tbody>
                        </table>
                    @endif
                </div>
            @endforeach
        @else
            <div style="text-align: center; padding: 50px 0;">
                <div style="color: #666; font-size: 16px;">Không có dữ liệu trong khoảng thời gian đã chọn.</div>
            </div>
        @endif

        <!-- Footer -->
        <div class="footer">
            <div>Báo cáo được tạo lúc: {{ vietnamese_long_date(now()) }}</div>
            <div>Hệ thống quản lý bán hàng</div>
        </div>
    </div>
</body>
</html>

<?php

use App\Http\Middleware\CheckTenantVisibility;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Support\Facades\Route;
use Stancl\Tenancy\Middleware\InitializeTenancyByDomain;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        using: function () {
            foreach (config('tenancy.identification.central_domains') as $domain) {
                Route::middleware('web')
                    ->domain($domain)
                    ->group(base_path('routes/web.php'));
            }
            //            Route::middleware('web')->group(base_path('routes/tenant.php'));
            // Route dành cho tenant
            Route::middleware([
                'web',
                InitializeTenancyByDomain::class,
                CheckTenantVisibility::class,
            ])->group(function () {
                require base_path('routes/tenant.php');

                // Load Shopper route gốc nhưng chạy trong tenant context
                require base_path('vendor/shopper/framework/packages/admin/routes/web.php');
                require base_path('vendor/shopper/framework/packages/admin/routes/auth.php');
                require base_path('vendor/shopper/framework/packages/admin/routes/cpanel.php');
                require base_path('vendor/shopper/framework/packages/admin/routes/admin/collection.php');
                require base_path('vendor/shopper/framework/packages/admin/routes/admin/customer.php');
                require base_path('vendor/shopper/framework/packages/admin/routes/admin/order.php');
                require base_path('vendor/shopper/framework/packages/admin/routes/admin/product.php');
                require base_path('vendor/shopper/framework/packages/admin/routes/admin/setting.php');
            });
        },
        commands: __DIR__ . '/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->group('universal', []);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
